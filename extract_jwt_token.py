#!/usr/bin/env python3
"""
Practical JWT Token Extractor for Totango Downloads
Extracts tokens from browser sessions and provides manual token support
"""

import json
import requests
import re
import base64
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("TokenExtractor")

class TotangoTokenExtractor:
    
    def __init__(self, config_file):
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        self.session = requests.Session()
    
    def extract_jwt_from_browser_session(self) -> str:
        """
        Extract JWT token from browser session cookies/headers
        This checks all possible locations where Totango might store tokens
        """
        logger.info("🔍 Extracting JWT token from browser session...")
        
        headers = self.config['totango']['headers']
        
        # Method 1: Check Authorization header
        auth_header = headers.get('Authorization', '')
        if 'Bearer ' in auth_header:
            token = auth_header.replace('Bearer ', '').strip()
            if self.is_valid_jwt(token):
                logger.info("✅ Found JWT in Authorization header")
                return token
        
        # Method 2: Parse cookies for JWT patterns  
        cookie_header = headers.get('Cookie', '')
        if cookie_header:
            # Split cookies and check each one
            cookies = cookie_header.split(';')
            
            for cookie in cookies:
                if '=' in cookie:
                    name, value = cookie.split('=', 1)
                    value = value.strip()
                    
                    # Check if this looks like a JWT
                    if self.is_valid_jwt(value):
                        logger.info(f"✅ Found JWT in cookie: {name.strip()}")
                        return value
                    
                    # Also check URL-decoded values
                    import urllib.parse
                    try:
                        decoded_value = urllib.parse.unquote(value)
                        if self.is_valid_jwt(decoded_value):
                            logger.info(f"✅ Found JWT in URL-decoded cookie: {name.strip()}")
                            return decoded_value
                    except:
                        pass
        
        # Method 3: Look for JWT patterns anywhere in headers
        header_text = json.dumps(headers)
        jwt_matches = re.findall(r'[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+', header_text)
        
        for match in jwt_matches:
            if len(match) > 100 and self.is_valid_jwt(match):
                logger.info("✅ Found JWT pattern in headers")
                return match
        
        logger.warning("⚠️ No JWT token found in browser session")
        return ""
    
    def is_valid_jwt(self, token: str) -> bool:
        """Check if a string looks like a valid JWT token"""
        if not token or len(token) < 50:
            return False
        
        # JWT has 3 parts separated by dots
        parts = token.split('.')
        if len(parts) != 3:
            return False
        
        try:
            # Try to decode the header (first part)
            header = parts[0]
            # Add padding if needed
            header += '=' * (4 - len(header) % 4)
            decoded_header = base64.b64decode(header, validate=True)
            header_json = json.loads(decoded_header)
            
            # Should have 'alg' and 'typ' fields
            if 'alg' in header_json and 'typ' in header_json:
                return True
                
        except:
            pass
        
        return False
    
    def decode_jwt_info(self, token: str) -> dict:
        """Decode JWT token to show expiration and other info"""
        try:
            parts = token.split('.')
            if len(parts) != 3:
                return {}
            
            # Decode payload (second part)
            payload = parts[1]
            payload += '=' * (4 - len(payload) % 4)
            decoded_payload = base64.b64decode(payload, validate=True)
            payload_json = json.loads(decoded_payload)
            
            # Convert timestamps to readable dates
            if 'iat' in payload_json:
                payload_json['issued_at'] = datetime.fromtimestamp(payload_json['iat']).strftime('%Y-%m-%d %H:%M:%S')
            
            if 'exp' in payload_json:
                payload_json['expires_at'] = datetime.fromtimestamp(payload_json['exp']).strftime('%Y-%m-%d %H:%M:%S')
                payload_json['is_expired'] = payload_json['exp'] < datetime.now().timestamp()
            
            return payload_json
            
        except Exception as e:
            logger.error(f"❌ Error decoding JWT: {e}")
            return {}
    
    def test_token_download(self, token: str, test_download_path: str = None) -> bool:
        """Test if a token works for downloading"""
        if not test_download_path:
            # Use a known test path from your data
            test_download_path = "/45606/account/001b000003nwKk1AAE/uploads/GYh4AYcBQNQJt_S2xPdz"
        
        try:
            # Construct the download URL
            path_parts = test_download_path.strip('/').split('/')
            service_id = path_parts[0]
            file_path = '/'.join(path_parts[1:])
            
            download_url = f"https://assets-proxy.totango.com/api/v2/assets/{service_id}/{file_path}?token={token}"
            
            logger.info(f"🧪 Testing token with: {download_url[:80]}...")
            
            response = self.session.head(download_url, timeout=30)
            
            if response.status_code == 200:
                logger.info(f"✅ Token works! File size: {response.headers.get('content-length', 'unknown')} bytes")
                return True
            elif response.status_code == 401:
                logger.error(f"❌ Token is invalid or expired (401)")
                return False
            elif response.status_code == 403:
                logger.error(f"❌ Token doesn't have permission (403)")
                return False
            else:
                logger.error(f"❌ Test failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Token test error: {e}")
            return False
    
    def get_token_instructions(self) -> str:
        """Provide instructions for manually getting a token"""
        return """
🔑 HOW TO GET A FRESH JWT TOKEN FROM TOTANGO:

Method 1 - Browser Developer Tools:
1. Open Totango in your browser and log in
2. Open Developer Tools (F12)
3. Go to Network tab
4. Try to download any file attachment
5. Look for requests to 'assets-proxy.totango.com'
6. Copy the token parameter from the URL

Method 2 - Extract from Page:
1. Go to any Totango page with attachments
2. Right-click → Inspect Element
3. Look in the page source for JWT tokens
4. Search for patterns like: "token":"eyJ..." 

Method 3 - Browser Console:
1. Open browser console on Totango page
2. Run: document.cookie
3. Look for JWT-like values in the cookies

The token format looks like:
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImYwMTEzMTc4LTM2NWQtMTFmMC04YTBjLTBhZmZmNzU2Y2M0ZiIsInNlcnZpY2VJZCI6IjQ1NjA2IiwiaWF0IjoxNzQ5MDMxMzYxLCJleHAiOjE3NDkxMTc3NjF9.LIQqBywmmO00TbMWYjHVSReiKf2n4NsDeXBRClAwRc0

Once you find a token, add it to your migration_config.json:
{
  "totango": {
    "jwt_token": "your_token_here"
  }
}
"""
    
    def run_token_extraction(self):
        """Run the complete token extraction process"""
        logger.info("🚀 Starting Totango JWT Token Extraction")
        logger.info("="*50)
        
        # Try to extract from current session
        extracted_token = self.extract_jwt_from_browser_session()
        
        if extracted_token:
            logger.info("✅ Extracted token from browser session!")
            
            # Decode and show token info
            token_info = self.decode_jwt_info(extracted_token)
            if token_info:
                logger.info("📋 Token Information:")
                for key, value in token_info.items():
                    logger.info(f"  {key}: {value}")
            
            # Test the token
            if self.test_token_download(extracted_token):
                logger.info("🎉 TOKEN IS WORKING! You can use this for migration.")
                
                # Save to config
                self.config['totango']['jwt_token'] = extracted_token
                config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
                with open(config_file, 'w') as f:
                    json.dump(self.config, f, indent=2)
                
                logger.info(f"💾 Token saved to migration_config.json")
                return True
            else:
                logger.error("❌ Extracted token doesn't work")
        
        # If extraction failed, show manual instructions
        logger.info("💡 Manual Token Instructions:")
        print(self.get_token_instructions())
        
        return False

def main():
    print("🔑 TOTANGO JWT TOKEN EXTRACTOR")
    print("="*40)
    
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    
    try:
        extractor = TotangoTokenExtractor(config_file)
        success = extractor.run_token_extraction()
        
        if success:
            print("\n🎉 TOKEN EXTRACTION SUCCESSFUL!")
            print("You can now run the enhanced migrator with working downloads.")
        else:
            print("\n💡 FOLLOW THE INSTRUCTIONS ABOVE")
            print("Get a fresh token and add it to your config file.")
        
    except Exception as e:
        logger.error(f"❌ Token extraction failed: {e}")

if __name__ == "__main__":
    main()
