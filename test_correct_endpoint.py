#!/usr/bin/env python3
"""
Test the CORRECT Gainsight upload endpoint
Based on debug findings: /v1/ant/attachments/upload returns 200
"""

import json
import requests
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("GainsightCorrectEndpointTest")

class GainsightCorrectEndpointTest:
    
    def __init__(self, config_file):
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        self.session = requests.Session()
    
    def test_correct_upload_endpoint(self):
        """Test the correct upload endpoint with proper format"""
        logger.info("🚀 Testing CORRECT Gainsight upload endpoint...")
        
        # Use the endpoint that returned 200 in our debug
        correct_upload_url = f"{self.config['gainsight']['url']}/v1/ant/attachments/upload"
        
        test_content = b"Test file content for correct endpoint validation"
        test_filename = "correct_endpoint_test.txt"
        
        logger.info(f"📤 Testing: {correct_upload_url}")
        
        # Test different formats with the correct endpoint
        
        # Format 1: Standard multipart
        logger.info("\n📋 FORMAT 1: Standard multipart upload")
        self.test_multipart_upload(correct_upload_url, test_filename, test_content)
        
        # Format 2: Simplified multipart
        logger.info("\n📋 FORMAT 2: Simplified multipart upload")
        self.test_simplified_upload(correct_upload_url, test_filename, test_content)
        
        # Format 3: Alternative field names
        logger.info("\n📋 FORMAT 3: Alternative field names")
        self.test_alternative_fields(correct_upload_url, test_filename, test_content)
    
    def test_multipart_upload(self, url: str, filename: str, content: bytes):
        """Test standard multipart upload"""
        try:
            files = {
                'file': (filename, content, 'text/plain')
            }
            
            form_data = {
                'contexts': json.dumps([{
                    'id': self.config['gainsight']['company_id'],
                    'base': True,
                    'obj': 'Company',
                    'lbl': self.config['gainsight']['company_name'],
                    'eid': None,
                    'eobj': 'Account',
                    'eurl': None,
                    'esys': 'SALESFORCE',
                    'dsp': True
                }]),
                'source': 'C360'
            }
            
            headers = {k: v for k, v in self.config['gainsight']['headers'].items() 
                      if k.lower() not in ['content-type', 'content-length']}
            
            response = self.session.post(url, files=files, data=form_data, headers=headers, timeout=60)
            self.log_detailed_response("Standard multipart", response)
            
        except Exception as e:
            logger.error(f"❌ Standard multipart error: {e}")
    
    def test_simplified_upload(self, url: str, filename: str, content: bytes):
        """Test simplified upload format"""
        try:
            files = {
                'file': (filename, content, 'text/plain')
            }
            
            form_data = {
                'contexts': json.dumps([{
                    'id': self.config['gainsight']['company_id'],
                    'obj': 'Company'
                }])
            }
            
            headers = {k: v for k, v in self.config['gainsight']['headers'].items() 
                      if k.lower() not in ['content-type', 'content-length']}
            
            response = self.session.post(url, files=files, data=form_data, headers=headers, timeout=60)
            self.log_detailed_response("Simplified", response)
            
        except Exception as e:
            logger.error(f"❌ Simplified upload error: {e}")
    
    def test_alternative_fields(self, url: str, filename: str, content: bytes):
        """Test with alternative field names"""
        try:
            # Try different file field names
            for file_field in ['file', 'attachment', 'upload', 'document']:
                logger.info(f"  Testing file field: {file_field}")
                
                files = {
                    file_field: (filename, content, 'text/plain')
                }
                
                form_data = {
                    'contexts': json.dumps([{
                        'id': self.config['gainsight']['company_id'],
                        'obj': 'Company'
                    }])
                }
                
                headers = {k: v for k, v in self.config['gainsight']['headers'].items() 
                          if k.lower() not in ['content-type', 'content-length']}
                
                response = self.session.post(url, files=files, data=form_data, headers=headers, timeout=60)
                
                logger.info(f"    {file_field} field response: {response.status_code}")
                if response.status_code == 200:
                    logger.info(f"    ✅ SUCCESS with {file_field} field!")
                    self.log_detailed_response(f"Alternative field ({file_field})", response)
                    return True
                elif response.status_code != 400:
                    logger.info(f"    Response: {response.text[:100]}")
            
        except Exception as e:
            logger.error(f"❌ Alternative fields error: {e}")
    
    def log_detailed_response(self, test_name: str, response):
        """Log detailed response information"""
        logger.info(f"  {test_name} response: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                logger.info(f"  ✅ SUCCESS!")
                logger.info(f"  Response keys: {list(result.keys())}")
                
                if result.get('result') and result.get('data'):
                    attachment_data = result['data']
                    attachment_id = attachment_data.get('id')
                    file_url = attachment_data.get('url', '')
                    
                    logger.info(f"  🎉 ATTACHMENT CREATED!")
                    logger.info(f"    ID: {attachment_id}")
                    logger.info(f"    URL: {file_url[:100]}...")
                    logger.info(f"    Size: {attachment_data.get('size', 'unknown')} bytes")
                    return True
                else:
                    logger.info(f"  📋 Response structure: {result}")
                    
            except json.JSONDecodeError:
                logger.info(f"  ✅ Success but not JSON: {response.text[:200]}")
                
        elif response.status_code == 400:
            logger.info(f"  ❌ Bad Request (400)")
            if response.text:
                logger.info(f"    Response: {response.text[:200]}")
                
        elif response.status_code == 500:
            logger.info(f"  ❌ Server Error (500)")
            if response.text:
                logger.info(f"    Response: {response.text[:200]}")
                
        else:
            logger.info(f"  ❌ Status: {response.status_code}")
            if response.text:
                logger.info(f"    Response: {response.text[:200]}")
        
        return False

def main():
    print("✅ TESTING CORRECT GAINSIGHT UPLOAD ENDPOINT")
    print("="*50)
    print("Found: /v1/ant/attachments/upload returns 200")
    print("Testing this endpoint with different formats...")
    print()
    
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    
    try:
        tester = GainsightCorrectEndpointTest(config_file)
        tester.test_correct_upload_endpoint()
        
        print("\n" + "="*60)
        print("🎯 CORRECT ENDPOINT TEST COMPLETED")
        print("="*60)
        print("If any test shows SUCCESS, we found the working format!")
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")

if __name__ == "__main__":
    main()
