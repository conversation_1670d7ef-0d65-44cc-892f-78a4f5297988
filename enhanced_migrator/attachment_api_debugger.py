#!/usr/bin/env python3
"""
🔧 Gainsight Attachment API Debugger
Systematically tests different API endpoints and formats to fix 400 errors

Based on the attachment format you provided, this will test:
1. Different API endpoints (/v1/ant/attachments vs /v1/ant/storage)
2. Different payload structures
3. Different authentication methods
4. Different file upload formats
"""

import requests
import json
import logging
import tempfile
import os
from datetime import datetime

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("AttachmentDebugger")

class GainsightAttachmentDebugger:
    
    def __init__(self, config_file):
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        self.session = requests.Session()
        
        # Create test file
        self.test_file_content = b"Test attachment content for debugging Gainsight API"
        self.test_filename = "gainsight_api_test.txt"
        
    def create_test_activity_first(self) -> Optional[str]:
        """Create a test activity to get a valid entityId for attachment linking"""
        logger.info("🔄 Creating test activity to get valid entityId...")
        
        activity_payload = {
            'note': {
                'type': 'UPDATE',
                'subject': 'Test Activity for Attachment Debugging',
                'content': '<p>Test activity created to debug attachment upload API</p>',
                'plainText': 'Test activity created to debug attachment upload API',
                'activityDate': datetime.now().isoformat(),
                'customFields': {
                    'internalAttendees': [],
                    'externalAttendees': []
                }
            },
            'contexts': [{
                'id': self.config['gainsight']['company_id'],
                'base': True,
                'obj': 'Company',
                'lbl': self.config['gainsight']['company_name'],
                'eid': None,
                'eobj': 'Account',
                'eurl': None,
                'esys': 'SALESFORCE',
                'dsp': True
            }],
            'author': {
                'id': self.config['gainsight']['user_id'],
                'obj': 'User',
                'name': self.config['gainsight']['user_name'],
                'email': self.config['gainsight']['user_email'],
                'eid': None,
                'eobj': 'User',
                'epp': None,
                'esys': 'SALESFORCE',
                'sys': 'GAINSIGHT',
                'pp': ''
            },
            'meta': {
                'source': 'C360',
                'systemType': 'GAINSIGHT',
                'hasTask': False,
                'emailSent': False
            },
            'syncedToSFDC': False,
            'tasks': [],
            'attachments': [],
            'mentions': [],
            'relatedRecords': {}
        }
        
        try:
            # Create draft first
            draft_url = f"{self.config['gainsight']['url']}/v1/ant/v2/activity/drafts"
            draft_response = self.session.post(
                draft_url, 
                json=activity_payload, 
                headers=self.config['gainsight']['headers'], 
                timeout=60
            )
            
            logger.info(f"📝 Draft creation: {draft_response.status_code}")
            
            if draft_response.status_code == 200:
                draft_data = draft_response.json()
                draft_id = draft_data.get('data', {}).get('id')
                
                if draft_id:
                    logger.info(f"✅ Draft created with ID: {draft_id}")
                    
                    # Create timeline entry
                    timeline_payload = activity_payload.copy()
                    timeline_payload['id'] = draft_id
                    
                    timeline_url = f"{self.config['gainsight']['url']}/v1/ant/v2/activity"
                    timeline_response = self.session.post(
                        timeline_url,
                        json=timeline_payload,
                        headers=self.config['gainsight']['headers'],
                        timeout=60
                    )
                    
                    logger.info(f"📋 Timeline creation: {timeline_response.status_code}")
                    
                    if timeline_response.status_code == 200:
                        logger.info(f"✅ Activity created successfully: {draft_id}")
                        return draft_id
                    else:
                        logger.error(f"❌ Timeline creation failed: {timeline_response.text[:200]}")
                else:
                    logger.error("❌ No draft ID returned")
            else:
                logger.error(f"❌ Draft creation failed: {draft_response.text[:200]}")
                
        except Exception as e:
            logger.error(f"❌ Error creating activity: {e}")
        
        return None
    
    def test_attachment_endpoints(self, entity_id: str):
        """Test different attachment API endpoints and formats"""
        logger.info("🧪 Testing different attachment API endpoints...")
        
        endpoints_to_test = [
            {
                'name': 'Storage API (v1/ant/storage)',
                'url': f"{self.config['gainsight']['url']}/v1/ant/storage",
                'method': self.test_storage_api
            },
            {
                'name': 'Attachments API (v1/ant/attachments)', 
                'url': f"{self.config['gainsight']['url']}/v1/ant/attachments",
                'method': self.test_attachments_api
            },
            {
                'name': 'Direct File Upload (v1/files)',
                'url': f"{self.config['gainsight']['url']}/v1/files",
                'method': self.test_files_api
            },
            {
                'name': 'Activity Attachments (v1/ant/v2/activity/attachments)',
                'url': f"{self.config['gainsight']['url']}/v1/ant/v2/activity/{entity_id}/attachments",
                'method': self.test_activity_attachments_api
            }
        ]
        
        for endpoint in endpoints_to_test:
            logger.info(f"\n📋 Testing: {endpoint['name']}")
            logger.info(f"   URL: {endpoint['url']}")
            
            try:
                success = endpoint['method'](endpoint['url'], entity_id)
                if success:
                    logger.info(f"🎉 SUCCESS! {endpoint['name']} works!")
                    return endpoint
                else:
                    logger.info(f"❌ {endpoint['name']} failed")
            except Exception as e:
                logger.error(f"❌ {endpoint['name']} error: {e}")
        
        logger.info("❌ All endpoints failed")
        return None
    
    def test_storage_api(self, url: str, entity_id: str) -> bool:
        """Test the storage API endpoint (most likely correct one)"""
        try:
            with tempfile.NamedTemporaryFile(mode='w+b', suffix='.txt', delete=False) as f:
                f.write(self.test_file_content)
                temp_path = f.name
            
            with open(temp_path, 'rb') as f:
                files = {
                    'file': (self.test_filename, f, 'text/plain')
                }
                
                # Test different form data combinations
                form_data_variants = [
                    # Variant 1: Based on your attachment example structure
                    {
                        'entityType': 'activity',
                        'entityId': entity_id,
                        'source': 'C360',
                        'type': 'attachment',
                        'description': 'Test attachment uploaded via API',
                        'createdBy': self.config['gainsight']['user_name']
                    },
                    # Variant 2: Simplified
                    {
                        'entityId': entity_id,
                        'entityType': 'activity'
                    },
                    # Variant 3: With contexts (like your original tests)
                    {
                        'entityId': entity_id,
                        'contexts': json.dumps([{
                            'id': self.config['gainsight']['company_id'],
                            'obj': 'Company',
                            'base': True
                        }]),
                        'source': 'C360'
                    }
                ]
                
                for i, form_data in enumerate(form_data_variants, 1):
                    logger.info(f"   Variant {i}: {list(form_data.keys())}")
                    
                    headers = {k: v for k, v in self.config['gainsight']['headers'].items() 
                              if k.lower() not in ['content-type', 'content-length']}
                    
                    response = self.session.post(
                        url,
                        files=files,
                        data=form_data,
                        headers=headers,
                        timeout=60
                    )
                    
                    logger.info(f"   Response: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            if result.get('data'):
                                logger.info(f"   ✅ Success! Attachment ID: {result['data'].get('id')}")
                                os.unlink(temp_path)
                                return True
                        except:
                            pass
                    elif response.status_code != 400:  # Not a client error, might be progress
                        logger.info(f"   Status {response.status_code}: {response.text[:100]}")
                    else:
                        logger.info(f"   400 Error: {response.text[:200]}")
            
            os.unlink(temp_path)
            return False
            
        except Exception as e:
            logger.error(f"Storage API test error: {e}")
            return False
    
    def test_attachments_api(self, url: str, entity_id: str) -> bool:
        """Test the attachments API endpoint"""
        try:
            with tempfile.NamedTemporaryFile(mode='w+b', suffix='.txt', delete=False) as f:
                f.write(self.test_file_content)
                temp_path = f.name
            
            with open(temp_path, 'rb') as f:
                files = {
                    'file': (self.test_filename, f, 'text/plain')
                }
                
                form_data = {
                    'entityId': entity_id,
                    'contexts': json.dumps([{
                        'id': self.config['gainsight']['company_id'],
                        'obj': 'Company',
                        'base': True
                    }]),
                    'source': 'C360',
                    'user': json.dumps({
                        'id': self.config['gainsight']['user_id'],
                        'obj': 'User',
                        'name': self.config['gainsight']['user_name'],
                        'email': self.config['gainsight']['user_email']
                    }),
                    'type': 'DEFAULT'
                }
                
                headers = {k: v for k, v in self.config['gainsight']['headers'].items() 
                          if k.lower() not in ['content-type', 'content-length']}
                
                response = self.session.post(
                    url,
                    files=files,
                    data=form_data,
                    headers=headers,
                    timeout=60
                )
                
                logger.info(f"   Response: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        if result.get('data'):
                            logger.info(f"   ✅ Success! Attachment ID: {result['data'].get('id')}")
                            os.unlink(temp_path)
                            return True
                    except:
                        pass
                else:
                    logger.info(f"   Error: {response.text[:200]}")
            
            os.unlink(temp_path)
            return False
            
        except Exception as e:
            logger.error(f"Attachments API test error: {e}")
            return False
    
    def test_files_api(self, url: str, entity_id: str) -> bool:
        """Test the files API endpoint"""
        try:
            with tempfile.NamedTemporaryFile(mode='w+b', suffix='.txt', delete=False) as f:
                f.write(self.test_file_content)
                temp_path = f.name
            
            with open(temp_path, 'rb') as f:
                files = {
                    'file': (self.test_filename, f, 'text/plain')
                }
                
                headers = {k: v for k, v in self.config['gainsight']['headers'].items() 
                          if k.lower() not in ['content-type', 'content-length']}
                
                response = self.session.post(
                    url,
                    files=files,
                    headers=headers,
                    timeout=60
                )
                
                logger.info(f"   Response: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        if result.get('data'):
                            logger.info(f"   ✅ Success! File ID: {result['data'].get('id')}")
                            os.unlink(temp_path)
                            return True
                    except:
                        pass
                else:
                    logger.info(f"   Error: {response.text[:200]}")
            
            os.unlink(temp_path)
            return False
            
        except Exception as e:
            logger.error(f"Files API test error: {e}")
            return False
    
    def test_activity_attachments_api(self, url: str, entity_id: str) -> bool:
        """Test the activity-specific attachments API endpoint"""
        try:
            with tempfile.NamedTemporaryFile(mode='w+b', suffix='.txt', delete=False) as f:
                f.write(self.test_file_content)
                temp_path = f.name
            
            with open(temp_path, 'rb') as f:
                files = {
                    'file': (self.test_filename, f, 'text/plain')
                }
                
                headers = {k: v for k, v in self.config['gainsight']['headers'].items() 
                          if k.lower() not in ['content-type', 'content-length']}
                
                response = self.session.post(
                    url,
                    files=files,
                    headers=headers,
                    timeout=60
                )
                
                logger.info(f"   Response: {response.status_code}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        if result.get('data'):
                            logger.info(f"   ✅ Success! Attachment ID: {result['data'].get('id')}")
                            os.unlink(temp_path)
                            return True
                    except:
                        pass
                else:
                    logger.info(f"   Error: {response.text[:200]}")
            
            os.unlink(temp_path)
            return False
            
        except Exception as e:
            logger.error(f"Activity attachments API test error: {e}")
            return False
    
    def test_different_auth_methods(self, working_endpoint: Dict, entity_id: str):
        """Test different authentication methods with the working endpoint"""
        logger.info("🔐 Testing different authentication methods...")
        
        auth_methods = [
            {
                'name': 'Current Cookie Auth',
                'headers': self.config['gainsight']['headers']
            },
            {
                'name': 'Cookie + User-Agent',
                'headers': {
                    **self.config['gainsight']['headers'],
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                    'Accept': 'application/json, text/plain, */*',
                    'Origin': self.config['gainsight']['url'],
                    'Referer': f"{self.config['gainsight']['url']}/v1/ui/customersuccess360"
                }
            },
            {
                'name': 'Cookie + X-Gs-Host',
                'headers': {
                    **self.config['gainsight']['headers'],
                    'X-Gs-Host': 'GAINSIGHT'
                }
            }
        ]
        
        for auth_method in auth_methods:
            logger.info(f"\n🔐 Testing: {auth_method['name']}")
            
            try:
                success = working_endpoint['method'](working_endpoint['url'], entity_id)
                if success:
                    logger.info(f"✅ {auth_method['name']} works!")
                    return auth_method
                else:
                    logger.info(f"❌ {auth_method['name']} failed")
            except Exception as e:
                logger.error(f"❌ {auth_method['name']} error: {e}")
        
        return None
    
    def run_complete_debug(self):
        """Run complete debugging workflow"""
        logger.info("🔧 GAINSIGHT ATTACHMENT API DEBUGGER")
        logger.info("="*50)
        
        # Step 1: Create test activity
        entity_id = self.create_test_activity_first()
        if not entity_id:
            logger.error("❌ Cannot proceed without valid activity ID")
            return False
        
        # Step 2: Test different endpoints
        working_endpoint = self.test_attachment_endpoints(entity_id)
        if not working_endpoint:
            logger.error("❌ No working endpoint found")
            return False
        
        # Step 3: Test authentication methods
        working_auth = self.test_different_auth_methods(working_endpoint, entity_id)
        
        # Step 4: Generate recommendation
        logger.info("\n" + "="*60)
        logger.info("🎯 DEBUGGING RESULTS & RECOMMENDATIONS")
        logger.info("="*60)
        
        if working_endpoint:
            logger.info(f"✅ WORKING ENDPOINT: {working_endpoint['name']}")
            logger.info(f"   URL: {working_endpoint['url']}")
            
            if working_auth:
                logger.info(f"✅ WORKING AUTH: {working_auth['name']}")
                
            logger.info("\n📝 RECOMMENDED IMPLEMENTATION:")
            logger.info("   Update your migrator to use:")
            logger.info(f"   - Endpoint: {working_endpoint['url']}")
            logger.info(f"   - Method: POST with multipart form data")
            logger.info(f"   - Required fields: entityId, entityType")
            logger.info(f"   - Auth: {working_auth['name'] if working_auth else 'Current cookie method'}")
            
            return True
        else:
            logger.error("❌ No working configuration found")
            logger.info("\n🔍 TROUBLESHOOTING SUGGESTIONS:")
            logger.info("   1. Check if your session is still valid")
            logger.info("   2. Verify company_id and user_id are correct")
            logger.info("   3. Try updating your session cookies")
            logger.info("   4. Check Gainsight API documentation for changes")
            
            return False

def main():
    print("🔧 GAINSIGHT ATTACHMENT API DEBUGGER")
    print("="*45)
    print("This will systematically test different API endpoints")
    print("and authentication methods to fix the 400 errors.")
    print("="*45)
    
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    
    try:
        debugger = GainsightAttachmentDebugger(config_file)
        success = debugger.run_complete_debug()
        
        if success:
            print("\n🎉 DEBUGGING SUCCESSFUL!")
            print("Use the recommended configuration in your migrator.")
        else:
            print("\n❌ DEBUGGING FAILED")
            print("Manual investigation required.")
            
    except Exception as e:
        logger.error(f"❌ Debugger failed: {e}")

if __name__ == "__main__":
    main()
