#!/usr/bin/env python3
"""
🚀 Quick Attachment Upload Test
Tests the most likely scenarios for Gainsight attachment uploads
"""

import requests
import json
import logging
import tempfile
import os

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("QuickAttachmentTest")

def load_config():
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    with open(config_file, 'r') as f:
        return json.load(f)

def create_test_activity(config):
    """Create a test activity and return its ID"""
    logger.info("Creating test activity...")
    
    payload = {
        'note': {
            'type': 'UPDATE',
            'subject': 'Test for Attachment Upload',
            'content': '<p>Test activity for attachment debugging</p>',
            'plainText': 'Test activity for attachment debugging',
            'activityDate': '2025-06-04T13:00:00.000Z'
        },
        'contexts': [{
            'id': config['gainsight']['company_id'],
            'base': True,
            'obj': 'Company',
            'lbl': config['gainsight']['company_name'],
            'esys': 'SALESFORCE',
            'dsp': True
        }],
        'author': {
            'id': config['gainsight']['user_id'],
            'obj': 'User',
            'name': config['gainsight']['user_name'],
            'email': config['gainsight']['user_email'],
            'esys': 'SALESFORCE',
            'sys': 'GAINSIGHT'
        },
        'meta': {
            'source': 'C360',
            'systemType': 'GAINSIGHT'
        }
    }
    
    session = requests.Session()
    
    # Create draft
    draft_url = f"{config['gainsight']['url']}/v1/ant/v2/activity/drafts"
    draft_response = session.post(draft_url, json=payload, headers=config['gainsight']['headers'])
    
    if draft_response.status_code == 200:
        draft_id = draft_response.json().get('data', {}).get('id')
        logger.info(f"✅ Draft created: {draft_id}")
        
        # Create timeline entry
        timeline_payload = payload.copy()
        timeline_payload['id'] = draft_id
        
        timeline_url = f"{config['gainsight']['url']}/v1/ant/v2/activity"
        timeline_response = session.post(timeline_url, json=timeline_payload, headers=config['gainsight']['headers'])
        
        if timeline_response.status_code == 200:
            logger.info(f"✅ Activity created: {draft_id}")
            return draft_id, session
        else:
            logger.error(f"❌ Timeline failed: {timeline_response.status_code} - {timeline_response.text[:200]}")
    else:
        logger.error(f"❌ Draft failed: {draft_response.status_code} - {draft_response.text[:200]}")
    
    return None, session

def test_attachment_upload(config, activity_id, session):
    """Test attachment upload with different approaches"""
    logger.info(f"Testing attachment upload for activity: {activity_id}")
    
    # Create test file
    test_content = b"Test attachment for Gainsight API debugging"
    filename = "test_attachment.txt"
    
    test_scenarios = [
        {
            'name': 'Storage API with basic fields',
            'url': f"{config['gainsight']['url']}/v1/ant/storage",
            'data': {
                'entityType': 'activity',
                'entityId': activity_id
            }
        },
        {
            'name': 'Storage API with extended fields',
            'url': f"{config['gainsight']['url']}/v1/ant/storage",
            'data': {
                'entityType': 'activity',
                'entityId': activity_id,
                'source': 'C360',
                'type': 'attachment'
            }
        },
        {
            'name': 'Attachments API with contexts',
            'url': f"{config['gainsight']['url']}/v1/ant/attachments",
            'data': {
                'entityId': activity_id,
                'contexts': json.dumps([{
                    'id': config['gainsight']['company_id'],
                    'obj': 'Company'
                }]),
                'source': 'C360'
            }
        },
        {
            'name': 'Activity-specific endpoint',
            'url': f"{config['gainsight']['url']}/v1/ant/v2/activity/{activity_id}/attachments",
            'data': {}
        },
        {
            'name': 'Files API',
            'url': f"{config['gainsight']['url']}/v1/files",
            'data': {
                'entityId': activity_id,
                'entityType': 'activity'
            }
        }
    ]
    
    for scenario in test_scenarios:
        logger.info(f"\n🧪 Testing: {scenario['name']}")
        logger.info(f"   URL: {scenario['url']}")
        
        try:
            with tempfile.NamedTemporaryFile(delete=False) as tmp_file:
                tmp_file.write(test_content)
                tmp_file.flush()
                
                with open(tmp_file.name, 'rb') as f:
                    files = {'file': (filename, f, 'text/plain')}
                    
                    headers = {k: v for k, v in config['gainsight']['headers'].items() 
                              if k.lower() not in ['content-type', 'content-length']}
                    
                    response = session.post(
                        scenario['url'],
                        files=files,
                        data=scenario['data'],
                        headers=headers,
                        timeout=60
                    )
                    
                    logger.info(f"   Status: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            result = response.json()
                            if result.get('data'):
                                logger.info(f"   🎉 SUCCESS! Attachment ID: {result['data'].get('id')}")
                                os.unlink(tmp_file.name)
                                return scenario
                        except:
                            logger.info(f"   ✅ Success but no JSON: {response.text[:100]}")
                    elif response.status_code == 201:
                        logger.info(f"   ✅ Created! Response: {response.text[:100]}")
                        os.unlink(tmp_file.name)
                        return scenario
                    else:
                        logger.info(f"   ❌ Failed: {response.text[:200]}")
                
                os.unlink(tmp_file.name)
                
        except Exception as e:
            logger.error(f"   ❌ Error: {e}")
    
    return None

def main():
    print("🚀 QUICK ATTACHMENT UPLOAD TEST")
    print("="*40)
    
    try:
        config = load_config()
        
        # Create test activity
        activity_id, session = create_test_activity(config)
        if not activity_id:
            print("❌ Cannot create test activity")
            return
        
        # Test attachment upload
        working_scenario = test_attachment_upload(config, activity_id, session)
        
        if working_scenario:
            print(f"\n🎉 FOUND WORKING SOLUTION!")
            print(f"✅ Method: {working_scenario['name']}")
            print(f"✅ URL: {working_scenario['url']}")
            print(f"✅ Data fields: {list(working_scenario['data'].keys())}")
            print("\nUpdate your migrator to use this configuration!")
        else:
            print("\n❌ NO WORKING SOLUTION FOUND")
            print("Additional investigation needed:")
            print("1. Check session validity")
            print("2. Verify permissions")
            print("3. Check API documentation")
            
    except Exception as e:
        logger.error(f"Test failed: {e}")

if __name__ == "__main__":
    main()
