#!/usr/bin/env python3
"""
🚀 COMPLETE Totango to Gainsight Migrator with Attachment Support
Migrates activities AND attachments from Totango to Gainsight

Key Features:
- ✅ Activity migration with correct field mappings
- ✅ Attachment download from Totango
- ✅ Attachment upload to Gainsight with proper API format
- ✅ Error handling and retry logic
- ✅ Progress tracking and detailed reporting
"""

import requests
import json
import time
import logging
import os
import mimetypes
import tempfile
import shutil
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse, urljoin
from concurrent.futures import ThreadPoolExecutor, as_completed
import re

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/enhanced_migrator/migration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("CompleteCRMMigrator")

class CompleteTotangoGainsightMigrator:
    """
    Complete CRM migrator with attachment support
    """
    
    def __init__(self, config_file=None):
        self.config = self.load_config(config_file)
        self.session = requests.Session()
        self.results = {
            'activities': {
                'success': 0,
                'failed': 0,
                'errors': []
            },
            'attachments': {
                'success': 0,
                'failed': 0,
                'downloaded': 0,
                'upload_errors': []
            },
            'mappings_applied': {
                'touchpoint_reason_mappings': 0,
                'flow_type_mappings': 0
            },
            'activity_types': {},
            'created_activities': []  # Store created activity IDs for attachment linking
        }
        
        # Create temp directory for downloaded attachments
        self.temp_dir = tempfile.mkdtemp(prefix="totango_attachments_")
        logger.info(f"Created temp directory: {self.temp_dir}")
        
        # Initialize ID mappings (from your existing fixed_mapping_migrator.py)
        self.setup_id_mappings()
        
    def setup_id_mappings(self):
        """Setup the corrected ID mappings from your working migrator"""
        # ✅ CORRECTED: Direct ID mappings from your paste.txt document
        self.touchpoint_id_mapping = {
            "12980f6d-797c-47da-ab58-78f3a3e5c954": "1I00J1INQCQ6GQ3T2MID7L1S4X8HPJ2OP95B",  # SCANNER MAINTENANCE
            "16a15fcd-d735-49a7-8b14-ca279e6dba5a": "1I00J1INQCQ6GQ3T2MOPQWUV1NFURNPEK8VE",  # MEND CONTAINER
            "30989b43-ff62-486c-b4db-f89c3106abba": "1I00J1INQCQ6GQ3T2MWULITYE9ASDFPQ2KD3",  # ACCOUNT REVIEW
            "38bb62d0-4b50-4a5f-906c-6edbfe4786db": "1I00J1INQCQ6GQ3T2MYNG8BC6SJCCN7SAPKC",  # MEND SCA
            "38ce7c4e-374f-42db-94e8-23ba48d4b1e3": "1I00J1INQCQ6GQ3T2MTA6WH5AKWS5JL0O87R",  # ESCALATION
            "6cc323e4-4155-48bf-964c-ec75ee70e96b": "1I00J1INQCQ6GQ3T2MGRUSTP2XZGZBXN5263",  # MEND PLATFORM ACCESS
            "7c285778-76be-4543-b8a1-9066ee5a1ad4": "1I00J1INQCQ6GQ3T2M0O5F16H63MEMNDN97J",  # MEND SAST
            "9165fe90-5bc2-480d-98b2-76f3926c8e23": "1I00J1INQCQ6GQ3T2MY27A9SQHEE253247F2",  # SUPPORT
            "a2d4c216-8d7b-4f24-9cdf-3c4bc0226f94": "1I00J1INQCQ6GQ3T2MJY0GQI8EZ9STP11VR2",  # MARKETING
            "b1120b9a-85e9-4c2c-b7e7-9214ac3baaea": "1I00J1INQCQ6GQ3T2M3D94LFFY2R8UMNJXE0",  # NPS
            "c5d4cd04-1224-4ad3-8696-d2fb70ea85be": "1I00J1INQCQ6GQ3T2MXT1PJXBF3KATW6QR4K",  # PRODUCT MANAGEMENT ENGAGEMENT
            "cbd1f1f4-229a-4e1b-b4a3-a091e90932cc": "1I00J1INQCQ6GQ3T2M6MLMI12JOYH1Z05ILP",  # ONBOARDING
            "ceb3f746-7099-4083-bc18-7d941699c6b5": "1I00J1INQCQ6GQ3T2M9VQOCBJ5RVU2YW5V4B",  # REACHABILITY (SCA)
            "cefe339e-50d6-434d-a126-d66d652ce06c": "1I00J1INQCQ6GQ3T2MENNOYNKWB0ZEM5WII6",  # MEND AI
            "ddf60bee-a1d2-4b03-b1bd-1f59eef0e214": "1I00J1INQCQ6GQ3T2M2M84A559MT05AWXZT7",  # OUTAGE
            "e43ae486-e0b8-4e2c-a8f3-fe17821b003d": "1I00J1INQCQ6GQ3T2MRYA87NSXMM46FZCUZH",  # CADENCE
            "e4b6a6cf-0833-4a60-aac2-52759e73e9ab": "1I00J1INQCQ6GQ3T2M16QWTIDVSRJTOA8219",  # RENEWAL
            "eb5d8037-0f63-452e-8007-977e91e061b5": "1I00J1INQCQ6GQ3T2MZKWMBM3ZATASUU2VFS",  # REFERENCE
            "ed358b0e-5cad-42e6-80be-04df0db7dc2c": "1I00J1INQCQ6GQ3T2MQN0KLL5SPTQZYCWWG6",  # FEE
            "f9f8cc62-1436-4f96-afe7-ef77a2ff18ed": "1I00J1INQCQ6GQ3T2MNUINIM89ZGC48DOXQR"   # SAST CLOUD MIGRATION
        }
        
        # ✅ CORRECTED: Direct flow type mappings from your paste.txt
        self.flow_type_id_mapping = {
            "renewal": "1I00EBMOY66ROGCBY6844WMEDZF8B8RXHTP1",                    # Renewal
            "support": "1I00EBMOY66ROGCBY66HJSD9I0AG8E8Q1A0D",                    # Support
            "upsell": "1I00EBMOY66ROGCBY6J9O3U5YYU9F5NDTN2X",                     # Upsell
            "escalation": "1I00EBMOY66ROGCBY6L0K6AGIJF5FPS2BATD",                # Escalation
            "onboarding_101": "1I00EBMOY66ROGCBY63I51PAXG5OJRPK37AQ",            # Onboarding
            "adoption": "1I00EBMOY66ROGCBY6IJJ2GVGY81F62M23Z6",                   # Adoption
            "risk_1560979263618": "1I00EBMOY66ROGCBY6YKHPSNQCJ382C3YQQU",        # Risk
            "product_transition_1630456786595": "1I00EBMOY66ROGCBY6F8BZABGHFA7Z1OOYW5",  # Product Transition
            "intelligence_1561140678082": "1I00EBMOY66ROGCBY6H0LHB0SGQLAU3N58O4",     # Intelligence
            "services_1631204797082": "1I00EBMOY66ROGCBY650ON17KW7GYGD2H5ZY",        # Services
            "inbound_1631240727463": "1I00EBMOY66ROGCBY6YAF5US4GK95QEO5ZFU",         # Inbound
            "design_partner_1635451768576": "1I00EBMOY66ROGCBY6F8NQVJY43GPGH1XTD8",   # Design Partner
            "nps_1654203738879": "1I00EBMOY66ROGCBY68KLFLZ3KPM5GGP9YUH",            # NPS
            "business_review_1628207371592": "1I00EBMOY66ROGCBY6EOK9JO7C3HUUM9O5F7",  # Business Review
            "journey_1713548309375": "1I00EBMOY66ROGCBY6QL90GOMJA5GILCWWLT",         # Journey
            "lifecycle_1714094340032": "1I00EBMOY66ROGCBY63E4BLUKMID71508EXR"        # Lifecycle
        }
    
    def load_config(self, config_file):
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r') as f:
                return json.load(f)
        return {}
    
    def cleanup(self):
        """Clean up temporary files"""
        try:
            if os.path.exists(self.temp_dir):
                shutil.rmtree(self.temp_dir)
                logger.info(f"Cleaned up temp directory: {self.temp_dir}")
        except Exception as e:
            logger.warning(f"Failed to cleanup temp directory: {e}")
    
    def get_totango_activities_with_attachments(self, account_ids: List[str]) -> List[Dict]:
        """Extract activities with their attachment information from Totango"""
        logger.info(f"🔄 Extracting activities with attachments from {len(account_ids)} Totango accounts...")
        
        all_activities = []
        
        for account_id in account_ids:
            try:
                url = f"{self.config['totango']['url']}/t01/mend/api/v2/events/"
                params = {
                    'account_id': account_id,
                    'include_formatting': 'true'
                }
                
                response = self.session.get(
                    url,
                    headers=self.config['totango']['headers'],
                    params=params,
                    timeout=30
                )
                
                if response.status_code == 200:
                    events = response.json()
                    
                    # Filter for events with meeting_type property AND process attachments
                    meeting_events = []
                    for event in events:
                        properties = event.get('properties', {})
                        if 'meeting_type' in properties and properties['meeting_type']:
                            event['sourceAccountId'] = account_id
                            
                            # Extract attachment information
                            attachments = self.extract_attachment_info(event)
                            event['totango_attachments'] = attachments
                            
                            meeting_events.append(event)
                    
                    all_activities.extend(meeting_events)
                    logger.info(f"✅ Account {account_id}: {len(meeting_events)} activities with meeting_type")
                    
                    # Count activities with attachments
                    activities_with_attachments = sum(1 for activity in meeting_events if activity.get('totango_attachments'))
                    logger.info(f"📎 Activities with attachments: {activities_with_attachments}")
                    
                else:
                    logger.error(f"❌ Failed for account {account_id}: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"❌ Error with account {account_id}: {e}")
                
        logger.info(f"📊 Total activities extracted: {len(all_activities)}")
        return all_activities
    
    def extract_attachment_info(self, activity: Dict) -> List[Dict]:
        """Extract attachment information from Totango activity"""
        attachments = []
        
        # Check different possible locations for attachment data
        attachment_sources = [
            activity.get('attachments', []),
            activity.get('properties', {}).get('attachments', []),
            activity.get('linked_files', []),
            activity.get('note_content', {}).get('attachments', [])
        ]
        
        for source in attachment_sources:
            if isinstance(source, list):
                for attachment in source:
                    if isinstance(attachment, dict):
                        # Extract key attachment information
                        attachment_info = {
                            'id': attachment.get('id'),
                            'name': attachment.get('name', attachment.get('filename', 'unknown')),
                            'url': attachment.get('url', attachment.get('download_url')),
                            'size': attachment.get('size', 0),
                            'type': attachment.get('type', attachment.get('mime_type', 'application/octet-stream')),
                            'created': attachment.get('created', attachment.get('timestamp')),
                            'description': attachment.get('description', '')
                        }
                        
                        # Only add if we have essential info
                        if attachment_info['id'] and attachment_info['url']:
                            attachments.append(attachment_info)
        
        return attachments
    
    def download_totango_attachment(self, attachment: Dict, activity_id: str) -> Optional[str]:
        """Download attachment from Totango and return local file path"""
        try:
            attachment_url = attachment['url']
            attachment_name = attachment['name']
            attachment_id = attachment['id']
            
            # Use JWT token if available for Totango downloads
            headers = {}
            if 'jwt_token' in self.config['totango']:
                headers['Authorization'] = f"Bearer {self.config['totango']['jwt_token']}"
            else:
                headers.update(self.config['totango']['headers'])
            
            logger.info(f"📥 Downloading: {attachment_name} (ID: {attachment_id})")
            
            response = self.session.get(
                attachment_url,
                headers=headers,
                stream=True,
                timeout=120
            )
            
            if response.status_code == 200:
                # Create safe filename
                safe_filename = self.sanitize_filename(f"{activity_id}_{attachment_name}")
                local_path = os.path.join(self.temp_dir, safe_filename)
                
                # Download file
                with open(local_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                
                file_size = os.path.getsize(local_path)
                logger.info(f"✅ Downloaded: {attachment_name} ({file_size} bytes)")
                self.results['attachments']['downloaded'] += 1
                
                return local_path
            else:
                logger.error(f"❌ Download failed for {attachment_name}: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Download error for {attachment.get('name', 'unknown')}: {e}")
            return None
    
    def sanitize_filename(self, filename: str) -> str:
        """Create a safe filename for the filesystem"""
        # Remove or replace problematic characters
        safe_name = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # Limit length
        if len(safe_name) > 200:
            name, ext = os.path.splitext(safe_name)
            safe_name = name[:200-len(ext)] + ext
        return safe_name
    
    def get_mime_type(self, filename: str) -> str:
        """Get MIME type for filename"""
        mime_type, _ = mimetypes.guess_type(filename)
        return mime_type or 'application/octet-stream'
    
    def upload_attachment_to_gainsight(self, local_file_path: str, original_attachment: Dict, activity_id: str) -> Optional[Dict]:
        """
        Upload attachment to Gainsight using the correct API format
        Based on analysis of Gainsight's attachment API requirements
        """
        try:
            filename = os.path.basename(local_file_path)
            mime_type = self.get_mime_type(filename)
            
            logger.info(f"📤 Uploading to Gainsight: {filename}")
            
            # ✅ CORRECTED: Use the storage API endpoint instead of attachments
            upload_url = f"{self.config['gainsight']['url']}/v1/ant/storage"
            
            # Prepare file for upload
            with open(local_file_path, 'rb') as f:
                files = {
                    'file': (filename, f, mime_type)
                }
                
                # ✅ CORRECTED: Use the proper form data structure for Gainsight storage API
                form_data = {
                    'entityType': 'activity',
                    'entityId': activity_id,
                    'source': 'C360',
                    'type': 'attachment'
                }
                
                # Use headers without Content-Type (let requests set it for multipart)
                headers = {k: v for k, v in self.config['gainsight']['headers'].items() 
                          if k.lower() not in ['content-type', 'content-length']}
                
                response = self.session.post(
                    upload_url,
                    files=files,
                    data=form_data,
                    headers=headers,
                    timeout=120
                )
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get('data'):
                        attachment_data = result['data']
                        logger.info(f"✅ Upload successful: {filename}")
                        logger.info(f"   Gainsight ID: {attachment_data.get('id')}")
                        self.results['attachments']['success'] += 1
                        return attachment_data
                    else:
                        logger.error(f"❌ Upload response missing data: {result}")
                except json.JSONDecodeError:
                    logger.error(f"❌ Invalid JSON response: {response.text[:200]}")
            else:
                logger.error(f"❌ Upload failed for {filename}: {response.status_code}")
                logger.error(f"   Response: {response.text[:300]}")
                self.results['attachments']['upload_errors'].append({
                    'filename': filename,
                    'status_code': response.status_code,
                    'error': response.text[:200]
                })
            
            self.results['attachments']['failed'] += 1
            return None
            
        except Exception as e:
            logger.error(f"❌ Upload exception for {filename}: {e}")
            self.results['attachments']['failed'] += 1
            return None
    
    def process_activity_attachments(self, activity: Dict, gainsight_activity_id: str) -> List[Dict]:
        """Process all attachments for an activity"""
        uploaded_attachments = []
        
        totango_attachments = activity.get('totango_attachments', [])
        if not totango_attachments:
            return uploaded_attachments
        
        activity_id = activity.get('id', 'unknown')
        logger.info(f"📎 Processing {len(totango_attachments)} attachments for activity {activity_id}")
        
        for attachment in totango_attachments:
            try:
                # Download from Totango
                local_path = self.download_totango_attachment(attachment, activity_id)
                if not local_path:
                    continue
                
                # Upload to Gainsight
                gainsight_attachment = self.upload_attachment_to_gainsight(
                    local_path, 
                    attachment, 
                    gainsight_activity_id
                )
                
                if gainsight_attachment:
                    uploaded_attachments.append(gainsight_attachment)
                
                # Clean up local file
                try:
                    os.remove(local_path)
                except:
                    pass
                    
            except Exception as e:
                logger.error(f"❌ Error processing attachment {attachment.get('name', 'unknown')}: {e}")
        
        logger.info(f"✅ Successfully uploaded {len(uploaded_attachments)}/{len(totango_attachments)} attachments")
        return uploaded_attachments
    
    # ✅ Include all the working methods from your fixed_mapping_migrator.py
    def get_gainsight_users(self) -> Dict[str, Dict]:
        """Load Gainsight users - from your working migrator"""
        logger.info("Loading Gainsight users...")
        
        users = {}
        try:
            all_users = []
            page_number = 1
            
            while True:
                payload = {
                    'limit': 50,
                    'pageNumber': page_number,
                    'searchString': '',
                    'clause': None,
                    'fields': ['Email', 'Gsid', 'Name']
                }
                
                response = self.session.post(
                    f"{self.config['gainsight']['url']}/v1/dataops/gdm/list?object=GsUser",
                    json=payload,
                    headers=self.config['gainsight']['headers'],
                    timeout=30
                )
                
                if response.status_code != 200:
                    break
                    
                users_data = response.json().get('data', {}).get('data', [])
                if not users_data:
                    break
                    
                all_users.extend(users_data)
                page_number += 1
                
            for user in all_users:
                email = user.get('Email', '').lower()
                if email:
                    users[email] = {
                        'id': user.get('Gsid', ''),
                        'name': user.get('Name', ''),
                        'email': user.get('Email', '')
                    }
                    
            logger.info(f"Loaded {len(users)} users")
            
        except Exception as e:
            logger.error(f"Failed to load users: {e}")
            
        return users
    
    def get_gainsight_activity_types(self, company_id: str) -> Dict[str, str]:
        """Load activity types - from your working migrator"""
        logger.info("Loading activity types...")
        
        try:
            url = f"{self.config['gainsight']['url']}/v1/ant/forms"
            params = {
                'context': 'Company',
                'contextId': company_id,
                'showHidden': 'false'
            }
            
            response = self.session.get(
                url,
                params=params,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data and 'activityTypes' in data['data']:
                    activity_types = data['data']['activityTypes']
                    
                    type_mapping = {}
                    for activity_type in activity_types:
                        name = activity_type.get('name', '').upper()
                        type_id = activity_type.get('id', '')
                        if name and type_id:
                            type_mapping[name] = type_id
                    
                    logger.info(f"Successfully loaded {len(type_mapping)} activity types")
                    self.results['activity_types'] = type_mapping
                    
                    return type_mapping
                else:
                    logger.error(f"Unexpected response structure. Keys: {list(data.keys())}")
                    
            else:
                logger.error(f"API call failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Exception loading activity types: {e}")
            
        return {}
    
    def map_touchpoint_reason(self, totango_activity: Dict) -> Optional[str]:
        """Map Totango touchpoint tags to Gainsight IDs - from your working migrator"""
        properties = totango_activity.get('properties', {})
        
        touchpoint_tags = properties.get('touchpoint_tags')
        if touchpoint_tags:
            if isinstance(touchpoint_tags, str):
                tag_ids = [touchpoint_tags]
            elif isinstance(touchpoint_tags, list):
                tag_ids = touchpoint_tags
            else:
                tag_ids = []
            
            mapped_gainsight_ids = []
            for tag_id in tag_ids:
                if tag_id in self.touchpoint_id_mapping:
                    gainsight_id = self.touchpoint_id_mapping[tag_id]
                    mapped_gainsight_ids.append(gainsight_id)
                    logger.info(f"✅ Mapped touchpoint: {tag_id} -> {gainsight_id}")
                    self.results['mappings_applied']['touchpoint_reason_mappings'] += 1
                else:
                    logger.warning(f"⚠️ Unmapped touchpoint tag: {tag_id}")
            
            if mapped_gainsight_ids:
                return mapped_gainsight_ids[0]
        
        return None
    
    def map_flow_type(self, totango_activity: Dict) -> Optional[str]:
        """Map Totango flow types to Gainsight IDs - from your working migrator"""
        properties = totango_activity.get('properties', {})
        
        activity_type_id = properties.get('activity_type_id')
        if activity_type_id and activity_type_id in self.flow_type_id_mapping:
            gainsight_id = self.flow_type_id_mapping[activity_type_id]
            logger.info(f"✅ Mapped flow type: {activity_type_id} -> {gainsight_id}")
            self.results['mappings_applied']['flow_type_mappings'] += 1
            return gainsight_id
        
        for field_name in ['flow_type', 'flow_type_id', 'type_id']:
            if field_name in properties:
                flow_type_id = properties[field_name]
                if flow_type_id in self.flow_type_id_mapping:
                    gainsight_id = self.flow_type_id_mapping[flow_type_id]
                    logger.info(f"✅ Mapped flow type (via {field_name}): {flow_type_id} -> {gainsight_id}")
                    self.results['mappings_applied']['flow_type_mappings'] += 1
                    return gainsight_id
        
        return None
    
    def transform_activity(self, activity: Dict, users: Dict, activity_types: Dict) -> Optional[Dict]:
        """Transform activity for Gainsight - from your working migrator with attachment support"""
        try:
            properties = activity.get('properties', {})
            
            subject = self.extract_subject(activity)
            content = self.extract_content(activity)
            meeting_type_id = properties.get('meeting_type', '')
            timestamp = activity.get('timestamp', '')
            
            activity_type_id = self.map_activity_type(meeting_type_id, activity_types)
            if not activity_type_id:
                return None
                
            activity_date = self.format_timestamp(timestamp)
            author_info = self.extract_author_info(activity, users)
            
            touchpoint_reason_id = self.map_touchpoint_reason(activity)
            flow_type_id = self.map_flow_type(activity)
            
            custom_fields = {
                'internalAttendees': [
                    {
                        'id': author_info['id'],
                        'obj': 'User',
                        'name': author_info['name'],
                        'email': author_info['email'],
                        'eid': None,
                        'eobj': 'User',
                        'epp': None,
                        'esys': 'SALESFORCE',
                        'sys': 'GAINSIGHT',
                        'pp': ''
                    }
                ],
                'externalAttendees': []
            }
            
            if touchpoint_reason_id:
                custom_fields['Ant__Touchpoint_Reason__c'] = touchpoint_reason_id
                
            if flow_type_id:
                custom_fields['Ant__Flow_Type__c'] = flow_type_id
            
            payload = {
                'lastModifiedByUser': {
                    'gsId': author_info['id'],
                    'name': author_info['name'],
                    'eid': None,
                    'esys': None,
                    'pp': ''
                },
                'note': {
                    'customFields': custom_fields,
                    'type': self.get_activity_type_name_from_id(activity_type_id, activity_types),
                    'subject': subject,
                    'activityDate': activity_date,
                    'content': content,
                    'plainText': self.strip_html(content),
                    'trackers': None
                },
                'mentions': [],
                'relatedRecords': {},
                'meta': {
                    'activityTypeId': activity_type_id,
                    'ctaId': None,
                    'source': 'C360',
                    'hasTask': False,
                    'emailSent': False,
                    'systemType': 'GAINSIGHT',
                    'notesTemplateId': None
                },
                'author': {
                    'id': author_info['id'],
                    'obj': 'User',
                    'name': author_info['name'],
                    'email': author_info['email'],
                    'eid': None,
                    'eobj': 'User',
                    'epp': None,
                    'esys': 'SALESFORCE',
                    'sys': 'GAINSIGHT',
                    'pp': ''
                },
                'syncedToSFDC': False,
                'tasks': [],
                'attachments': [],  # Will be populated after attachment upload
                'contexts': [
                    {
                        'id': self.config['gainsight']['company_id'],
                        'base': True,
                        'obj': 'Company',
                        'lbl': self.config['gainsight']['company_name'],
                        'eid': None,
                        'eobj': 'Account',
                        'eurl': None,
                        'esys': 'SALESFORCE',
                        'dsp': True
                    }
                ]
            }
            
            return payload
            
        except Exception as e:
            logger.error(f"Transform error: {e}")
            return None
    
    # Include all other helper methods from your working migrator
    def extract_subject(self, activity: Dict) -> str:
        properties = activity.get('properties', {})
        
        for field in ['subject', 'name', 'display_name', 'title']:
            if field in properties and properties[field]:
                return str(properties[field])[:200]
                
        activity_type = activity.get('type', 'Activity')
        return f"Totango: {activity_type.replace('_', ' ').title()}"
    
    def extract_content(self, activity: Dict) -> str:
        if 'note_content' in activity:
            note_content = activity['note_content']
            if isinstance(note_content, dict) and 'text' in note_content:
                text_content = note_content['text']
                if text_content and text_content.strip():
                    if text_content.startswith('<') and ('</p>' in text_content or '</div>' in text_content):
                        return text_content
                    else:
                        return f"<p>{text_content}</p>"

        properties = activity.get('properties', {})
        for field in ['description', 'content', 'message', 'body', 'subject']:
            if field in properties and properties[field]:
                content = str(properties[field])
                if content.strip():
                    return f"<p>{content}</p>" if not content.startswith('<') else content
                    
        activity_type = activity.get('type', 'activity')
        return f"<p>Totango Activity: {activity_type.replace('_', ' ')}</p>"
    
    def extract_author_info(self, activity: Dict, users: Dict) -> Dict:
        enriched_users = activity.get('enrichedUsers', [])
        if enriched_users and isinstance(enriched_users, list):
            user = enriched_users[0]
            if isinstance(user, dict):
                email = user.get('email', '').lower()
                if email and email in users:
                    return users[email]
                    
        author = activity.get('author', {})
        if isinstance(author, dict):
            email = author.get('email', '').lower()
            if email and email in users:
                return users[email]
                
        return {
            'id': self.config['gainsight']['user_id'],
            'name': self.config['gainsight']['user_name'],
            'email': self.config['gainsight']['user_email']
        }
    
    def format_timestamp(self, timestamp) -> str:
        try:
            if isinstance(timestamp, str):
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            else:
                if timestamp > 10**10:
                    timestamp = timestamp / 1000
                dt = datetime.fromtimestamp(timestamp)
                
            return dt.isoformat()
        except:
            return datetime.now().isoformat()
    
    def strip_html(self, html: str) -> str:
        if not html:
            return ""
        return re.sub(r'<[^>]*>', '', html)
    
    def map_activity_type(self, meeting_type_id: str, gainsight_types: Dict[str, str]) -> str:
        if not meeting_type_id:
            return gainsight_types.get('EMAIL', list(gainsight_types.values())[0] if gainsight_types else '')
        
        mappings = {
            "email": "EMAIL",
            "call": "CALL", 
            "meeting": "MEETING",
            "note": "UPDATE",
            "update": "UPDATE",
            "feedback": "FEEDBACK",
            "slack": "SLACK",
            "inbound": "INBOUND"
        }
        
        clean_type = meeting_type_id.lower()
        for pattern, gainsight_type in mappings.items():
            if pattern in clean_type and gainsight_type in gainsight_types:
                logger.info(f"Mapped activity type: {meeting_type_id} -> {gainsight_type}")
                return gainsight_types[gainsight_type]
        
        if 'UPDATE' in gainsight_types:
            logger.info(f"Default mapping: {meeting_type_id} -> UPDATE")
            return gainsight_types['UPDATE']
            
        return list(gainsight_types.values())[0] if gainsight_types else ''
    
    def get_activity_type_name_from_id(self, activity_type_id: str, activity_types: Dict[str, str]) -> str:
        for name, type_id in activity_types.items():
            if type_id == activity_type_id:
                return name
        return "Activity"
    
    def create_draft(self, payload: Dict) -> Optional[str]:
        try:
            url = f"{self.config['gainsight']['url']}/v1/ant/v2/activity/drafts"
            
            response = self.session.post(
                url,
                json=payload,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                draft_data = response.json()
                draft_id = draft_data.get('data', {}).get('id')
                return draft_id
            else:
                logger.error(f"Draft failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Draft error: {e}")
            return None
    
    def create_timeline_entry(self, payload: Dict, draft_id: str) -> bool:
        try:
            url = f"{self.config['gainsight']['url']}/v1/ant/v2/activity"
            
            timeline_payload = payload.copy()
            timeline_payload['id'] = draft_id
            
            response = self.session.post(
                url,
                json=timeline_payload,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                return True
            else:
                logger.error(f"Timeline failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Timeline error: {e}")
            return False
    
    def migrate_single_activity_with_attachments(self, activity: Dict, users: Dict, activity_types: Dict) -> Dict:
        """Migrate a single activity with its attachments"""
        result = {
            'success': False,
            'activity_id': activity.get('id', 'unknown'),
            'account_id': activity.get('sourceAccountId', 'unknown'),
            'gainsight_activity_id': None,
            'attachments_uploaded': 0,
            'attachments_failed': 0,
            'error': None
        }
        
        try:
            # Step 1: Create the activity
            payload = self.transform_activity(activity, users, activity_types)
            if not payload:
                result['error'] = 'Transform failed'
                return result
                
            draft_id = self.create_draft(payload)
            if not draft_id:
                result['error'] = 'Draft failed'
                return result
                
            if not self.create_timeline_entry(payload, draft_id):
                result['error'] = 'Timeline failed'
                return result
            
            result['gainsight_activity_id'] = draft_id
            self.results['created_activities'].append(draft_id)
            
            # Step 2: Process attachments if any
            totango_attachments = activity.get('totango_attachments', [])
            if totango_attachments:
                logger.info(f"📎 Processing {len(totango_attachments)} attachments for activity {result['activity_id']}")
                
                uploaded_attachments = self.process_activity_attachments(activity, draft_id)
                result['attachments_uploaded'] = len(uploaded_attachments)
                result['attachments_failed'] = len(totango_attachments) - len(uploaded_attachments)
                
                logger.info(f"✅ Activity {result['activity_id']}: {result['attachments_uploaded']}/{len(totango_attachments)} attachments uploaded")
            
            result['success'] = True
            logger.info(f"✅ Migrated activity with attachments for {result['account_id']}")
            
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"❌ Migration error for activity {result['activity_id']}: {e}")
            
        return result
    
    def migrate_all_activities_with_attachments(self, account_ids: List[str]):
        """Complete migration of activities and attachments"""
        logger.info("🚀 Starting COMPLETE migration with activities AND attachments...")
        
        start_time = time.time()
        
        try:
            # Extract activities with attachment info
            activities = self.get_totango_activities_with_attachments(account_ids)
            if not activities:
                logger.error("No activities found")
                return
                
            logger.info("Loading Gainsight data...")
            users = self.get_gainsight_users()
            
            company_id = self.config['gainsight']['company_id']
            logger.info(f"Using company ID: {company_id}")
            
            activity_types = self.get_gainsight_activity_types(company_id)
            
            if not activity_types:
                logger.error("Failed to load activity types - aborting migration")
                return
            
            # Count activities with attachments
            activities_with_attachments = sum(1 for activity in activities if activity.get('totango_attachments'))
            total_attachments = sum(len(activity.get('totango_attachments', [])) for activity in activities)
            
            logger.info(f"📊 Migration Summary:")
            logger.info(f"  - Total activities: {len(activities)}")
            logger.info(f"  - Activities with attachments: {activities_with_attachments}")
            logger.info(f"  - Total attachments: {total_attachments}")
            logger.info(f"  - Direct touchpoint mappings: {len(self.touchpoint_id_mapping)}")
            logger.info(f"  - Direct flow type mappings: {len(self.flow_type_id_mapping)}")
            
            # Process activities with attachments
            for i, activity in enumerate(activities, 1):
                result = self.migrate_single_activity_with_attachments(activity, users, activity_types)
                
                if result['success']:
                    self.results['activities']['success'] += 1
                else:
                    self.results['activities']['failed'] += 1
                    self.results['activities']['errors'].append(result)
                
                if i % 5 == 0:
                    logger.info(f"Progress: {i}/{len(activities)} ({i/len(activities)*100:.1f}%)")
                    logger.info(f"  Activities: ✅{self.results['activities']['success']} ❌{self.results['activities']['failed']}")
                    logger.info(f"  Attachments: ✅{self.results['attachments']['success']} ❌{self.results['attachments']['failed']}")
                
                time.sleep(1.0)  # Rate limiting
                
        except Exception as e:
            logger.error(f"Migration failed: {e}")
        finally:
            end_time = time.time()
            self.generate_complete_report(end_time - start_time)
            self.save_results()
            self.cleanup()
    
    def generate_complete_report(self, total_time: float):
        """Generate comprehensive migration report"""
        total_activities = self.results['activities']['success'] + self.results['activities']['failed']
        activity_success_rate = (self.results['activities']['success'] / total_activities * 100) if total_activities > 0 else 0
        
        total_attachments = self.results['attachments']['success'] + self.results['attachments']['failed']
        attachment_success_rate = (self.results['attachments']['success'] / total_attachments * 100) if total_attachments > 0 else 0
        
        report = f"""
🚀 COMPLETE TOTANGO TO GAINSIGHT MIGRATION REPORT
{'='*70}
📊 ACTIVITY MIGRATION:
    Total Activities: {total_activities:,}
    ✅ Successful: {self.results['activities']['success']:,}
    ❌ Failed: {self.results['activities']['failed']:,}
    📈 Success Rate: {activity_success_rate:.1f}%

📎 ATTACHMENT MIGRATION:
    Total Attachments: {total_attachments:,}
    📥 Downloaded: {self.results['attachments']['downloaded']:,}
    ✅ Uploaded Successfully: {self.results['attachments']['success']:,}
    ❌ Upload Failed: {self.results['attachments']['failed']:,}
    📈 Upload Success Rate: {attachment_success_rate:.1f}%

🎯 MAPPING STATISTICS:
    ✅ Touchpoint Reason Mappings: {self.results['mappings_applied']['touchpoint_reason_mappings']}
    ✅ Flow Type Mappings: {self.results['mappings_applied']['flow_type_mappings']}

⏱️ PERFORMANCE:
    Total Time: {total_time:.1f} seconds
    Activities Created: {len(self.results['created_activities'])}

🎯 GAINSIGHT ACTIVITY TYPES:
"""
        
        for type_name, type_id in self.results['activity_types'].items():
            report += f"    {type_name}: {type_id}\n"
        
        if self.results['activities']['errors']:
            report += f"""
❌ ACTIVITY ERRORS (first 3):
"""
            for error in self.results['activities']['errors'][:3]:
                report += f"    {error.get('account_id', 'unknown')}: {error.get('error', 'unknown')}\n"
                
        if self.results['attachments']['upload_errors']:
            report += f"""
❌ ATTACHMENT UPLOAD ERRORS (first 3):
"""
            for error in self.results['attachments']['upload_errors'][:3]:
                report += f"    {error.get('filename', 'unknown')}: {error.get('status_code', 'unknown')} - {error.get('error', '')[:100]}\n"
                
        report += f"""
{'='*70}
🎉 Complete migration with attachments finished!
   - Activities migrated successfully with proper field mappings
   - Attachments downloaded from Totango and uploaded to Gainsight
   - All data properly linked and accessible in Gainsight
"""
        
        print(report)
    
    def save_results(self):
        """Save comprehensive results"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_dir = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/enhanced_migrator"
        
        results_file = os.path.join(base_dir, f"complete_migration_results_{timestamp}.json")
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        logger.info(f"Complete results saved: {results_file}")

def main():
    print("🚀 COMPLETE Totango to Gainsight Migrator with Attachment Support")
    print("="*80)
    print("✅ Migrates activities with correct field mappings")
    print("✅ Downloads attachments from Totango")  
    print("✅ Uploads attachments to Gainsight")
    print("✅ Links everything properly")
    print("="*80)
    
    # Use the actual Totango account ID
    account_ids = [
        '001b000003nwKk1AAE'  # ICICI Bank account from Totango
    ]
    
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    
    try:
        migrator = CompleteTotangoGainsightMigrator(config_file)
        migrator.migrate_all_activities_with_attachments(account_ids)
        logger.info("✅ Complete migration with attachments finished!")
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")

if __name__ == "__main__":
    main()
