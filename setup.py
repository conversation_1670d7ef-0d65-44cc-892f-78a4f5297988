#!/usr/bin/env python3
"""
🚀 Enhanced Totango to Gainsight Migration - Setup and Test
===========================================================
Quick setup and validation script for the migration tool
"""

import os
import json
import sys
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed"""
    print("🔍 Checking dependencies...")
    
    try:
        import requests
        print("✅ requests library installed")
        return True
    except ImportError:
        print("❌ requests library not found")
        print("💡 Install with: pip install requests")
        return False

def create_config_template():
    """Create configuration template"""
    config_path = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    
    if os.path.exists(config_path):
        print(f"✅ Configuration file already exists: {config_path}")
        return config_path
    
    print("📝 Creating configuration template...")
    
    config = {
        "totango": {
            "url": "https://app.totango.com",
            "headers": {
                "Cookie": "your_totango_session_cookie_here"
            }
        },
        "gainsight": {
            "url": "https://demo-emea1.gainsightcloud.com",
            "headers": {
                "Content-Type": "application/json",
                "Cookie": "your_gainsight_session_cookie_here"
            },
            "user_id": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER",
            "user_email": "<EMAIL>",
            "user_name": "Ram Prasad",
            "company_id": "1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU",
            "company_name": "ICICI"
        },
        "migration": {
            "batch_size": 5,
            "parallel_workers": 3,
            "rate_limit_delay": 0.5,
            "max_retries": 3
        }
    }
    
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    print(f"✅ Configuration template created: {config_path}")
    return config_path

def validate_config(config_path):
    """Validate configuration file"""
    print("🔍 Validating configuration...")
    
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # Check required sections
        required_sections = ['totango', 'gainsight', 'migration']
        for section in required_sections:
            if section not in config:
                print(f"❌ Missing section: {section}")
                return False
        
        # Check for placeholder values
        totango_cookie = config['totango']['headers']['Cookie']
        gainsight_cookie = config['gainsight']['headers']['Cookie']
        
        if 'your_totango_session_cookie_here' in totango_cookie:
            print("⚠️ Totango cookie still contains placeholder")
            print("💡 Please update with your actual Totango session cookie")
            return False
        
        if 'your_gainsight_session_cookie_here' in gainsight_cookie:
            print("⚠️ Gainsight cookie still contains placeholder")
            print("💡 Please update with your actual Gainsight session cookie")
            return False
        
        print("✅ Configuration validation passed")
        return True
        
    except Exception as e:
        print(f"❌ Configuration validation failed: {e}")
        return False

def test_api_connectivity():
    """Test API connectivity (basic check)"""
    print("🌐 Testing API connectivity...")
    
    try:
        import requests
        
        # Test basic connectivity (without actual API calls)
        print("✅ Network connectivity available")
        print("💡 Actual API testing will happen during migration")
        return True
        
    except Exception as e:
        print(f"❌ Network connectivity test failed: {e}")
        return False

def create_test_account_list():
    """Create a sample test account list"""
    test_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/test_account_ids.py"
    
    if os.path.exists(test_file):
        print(f"✅ Test account file already exists: {test_file}")
        return test_file
    
    print("📝 Creating test account list template...")
    
    content = '''#!/usr/bin/env python3
"""
Test Account IDs for Migration
===============================
Replace these with your actual Totango account IDs
"""

# Test with a small set of accounts first
TEST_ACCOUNT_IDS = [
    'account_id_1',  # Replace with actual account ID
    'account_id_2',  # Replace with actual account ID
    'account_id_3'   # Replace with actual account ID
]

# Full migration account list (add all your account IDs here)
FULL_ACCOUNT_IDS = [
    # Add all your Totango account IDs here
    # You can get these from Totango API or export
]

# Use this for testing
if __name__ == "__main__":
    print("Test Account IDs:")
    for i, account_id in enumerate(TEST_ACCOUNT_IDS, 1):
        print(f"  {i}. {account_id}")
    
    print(f"\\nTotal test accounts: {len(TEST_ACCOUNT_IDS)}")
    print(f"Total full accounts: {len(FULL_ACCOUNT_IDS)}")
'''
    
    with open(test_file, 'w') as f:
        f.write(content)
    
    print(f"✅ Test account list created: {test_file}")
    return test_file

def run_setup():
    """Run complete setup process"""
    print("🚀 Enhanced Totango to Gainsight Migration - Setup")
    print("="*60)
    
    # Step 1: Check dependencies
    if not check_dependencies():
        print("\n❌ Setup failed: Missing dependencies")
        return False
    
    # Step 2: Create configuration
    config_path = create_config_template()
    
    # Step 3: Validate configuration
    config_valid = validate_config(config_path)
    
    # Step 4: Test connectivity
    connectivity_ok = test_api_connectivity()
    
    # Step 5: Create test account list
    test_file = create_test_account_list()
    
    # Summary
    print("\n" + "="*60)
    print("📋 SETUP SUMMARY")
    print("="*60)
    
    if config_valid and connectivity_ok:
        print("✅ Setup completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Update your API credentials in:")
        print(f"   📝 {config_path}")
        print("2. Add your account IDs to:")
        print(f"   📝 {test_file}")
        print("3. Run the migration:")
        print("   🚀 python enhanced_totango_gainsight_migrator_complete.py")
        print("\n💡 Start with test accounts first to validate the setup")
        return True
    else:
        print("⚠️ Setup completed with warnings")
        print("\n📋 Action Required:")
        if not config_valid:
            print("1. Update your API credentials in:")
            print(f"   📝 {config_path}")
        print("2. Add your account IDs to:")
        print(f"   📝 {test_file}")
        print("3. Re-run setup to validate: python setup.py")
        return False

def show_credential_help():
    """Show help for getting API credentials"""
    print("\n🔧 HOW TO GET API CREDENTIALS")
    print("="*40)
    
    print("\n📡 TOTANGO CREDENTIALS:")
    print("1. Open Totango in your browser")
    print("2. Open Developer Tools (F12)")
    print("3. Go to Network tab")
    print("4. Navigate to any page in Totango")
    print("5. Find a request to 'app.totango.com'")
    print("6. Copy the 'Cookie' header value")
    print("7. Paste it in the config file")
    
    print("\n📡 GAINSIGHT CREDENTIALS:")
    print("1. Open Gainsight in your browser")
    print("2. Open Developer Tools (F12)")
    print("3. Go to Network tab")
    print("4. Navigate to C360 Timeline")
    print("5. Find a request to your Gainsight instance")
    print("6. Copy the 'Cookie' header value")
    print("7. Note your user ID and company ID from responses")
    print("8. Update all values in the config file")

if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--help-credentials":
        show_credential_help()
    else:
        success = run_setup()
        if not success:
            print("\n💡 For credential help, run: python setup.py --help-credentials")
