#!/usr/bin/env python3
"""
Exact Browser Request Mimic for Gainsight Upload
Mimics the exact headers and format from the successful browser request
"""

import json
import requests
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("BrowserMimicTest")

class BrowserMimicUploadTest:
    
    def __init__(self, config_file):
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        self.session = requests.Session()
    
    def get_browser_headers(self):
        """Get headers that exactly match the successful browser request"""
        return {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate, br, zstd',
            'Accept-Language': 'en-GB,en-US;q=0.9,en;q=0.8',
            '<PERSON>ie': self.config['gainsight']['headers']['<PERSON>ie'],
            'Origin': self.config['gainsight']['url'],
            'Referer': f"{self.config['gainsight']['url']}/v1/ui/customersuccess360?cid={self.config['gainsight']['company_id']}",
            'Sec-Ch-Ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"macOS"',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors', 
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            'X-Gs-Host': 'GAINSIGHT'
        }
    
    def test_exact_browser_format(self):
        """Test the exact format used by the browser"""
        logger.info("🔄 Testing EXACT browser request format...")
        
        # Use the original endpoint (browser shows it works)
        upload_url = f"{self.config['gainsight']['url']}/v1/ant/attachments"
        
        test_content = b"Test file content matching browser format"
        test_filename = "browser_format_test.txt"
        
        logger.info(f"📤 Testing: {upload_url}")
        logger.info(f"📋 Using exact browser headers...")
        
        # Test with exact browser headers
        headers = self.get_browser_headers()
        
        # Remove Content-Type to let requests set it for multipart
        if 'Content-Type' in headers:
            del headers['Content-Type']
        
        files = {
            'file': (test_filename, test_content, 'text/plain')
        }
        
        # Try different form data formats that might match browser
        test_formats = [
            # Format 1: Full context (like our previous attempts)
            {
                'contexts': json.dumps([{
                    'id': self.config['gainsight']['company_id'],
                    'base': True,
                    'obj': 'Company',
                    'lbl': self.config['gainsight']['company_name'],
                    'eid': None,
                    'eobj': 'Account',
                    'eurl': None,
                    'esys': 'SALESFORCE',
                    'dsp': True
                }]),
                'source': 'C360'
            },
            
            # Format 2: Minimal context
            {
                'contexts': json.dumps([{
                    'id': self.config['gainsight']['company_id'],
                    'obj': 'Company'
                }])
            },
            
            # Format 3: Just company ID
            {
                'companyId': self.config['gainsight']['company_id']
            },
            
            # Format 4: Context as individual fields
            {
                'contextId': self.config['gainsight']['company_id'],
                'contextType': 'Company'
            },
            
            # Format 5: Empty form data (just file)
            {}
        ]
        
        for i, form_data in enumerate(test_formats, 1):
            logger.info(f"\n📋 FORMAT {i}: {list(form_data.keys()) if form_data else 'File only'}")
            
            try:
                response = self.session.post(
                    upload_url,
                    files=files,
                    data=form_data,
                    headers=headers,
                    timeout=60
                )
                
                logger.info(f"  Response: {response.status_code}")
                
                if response.status_code == 200:
                    logger.info(f"  🎉 SUCCESS! Format {i} works!")
                    self.log_success_details(response)
                    return True
                elif response.status_code == 400:
                    logger.info(f"  ❌ Bad Request (400) - Wrong format")
                    if response.text:
                        logger.info(f"    Response: {response.text[:200]}")
                elif response.status_code == 405:
                    logger.info(f"  ❌ Method Not Allowed (405) - Still wrong")
                else:
                    logger.info(f"  ❌ Status: {response.status_code}")
                    if response.text:
                        logger.info(f"    Response: {response.text[:100]}")
                        
            except Exception as e:
                logger.error(f"  ❌ Error: {e}")
        
        return False
    
    def log_success_details(self, response):
        """Log details of successful response"""
        try:
            result = response.json()
            logger.info(f"  📋 Response keys: {list(result.keys())}")
            
            if result.get('result') and result.get('data'):
                attachment_data = result['data']
                attachment_id = attachment_data.get('id')
                file_url = attachment_data.get('url', '')
                
                logger.info(f"  🎉 ATTACHMENT UPLOADED!")
                logger.info(f"    ID: {attachment_id}")
                logger.info(f"    URL: {file_url[:100]}...")
                logger.info(f"    Size: {attachment_data.get('size', 'unknown')} bytes")
            else:
                logger.info(f"  📋 Full response: {result}")
                
        except json.JSONDecodeError:
            logger.info(f"  ✅ Success but not JSON: {response.text[:200]}")
    
    def test_header_variations(self):
        """Test which headers are actually required"""
        logger.info("\n🔍 Testing which headers are required...")
        
        upload_url = f"{self.config['gainsight']['url']}/v1/ant/attachments"
        test_content = b"Header test content"
        test_filename = "header_test.txt"
        
        files = {
            'file': (test_filename, test_content, 'text/plain')
        }
        
        form_data = {
            'contexts': json.dumps([{
                'id': self.config['gainsight']['company_id'],
                'obj': 'Company'
            }])
        }
        
        # Test with minimal headers first
        minimal_headers = {
            'Cookie': self.config['gainsight']['headers']['Cookie']
        }
        
        logger.info("📋 Testing minimal headers (Cookie only):")
        response = self.session.post(upload_url, files=files, data=form_data, headers=minimal_headers, timeout=60)
        logger.info(f"  Response: {response.status_code}")
        
        if response.status_code == 200:
            logger.info("  ✅ SUCCESS! Only Cookie header needed!")
            return True
        
        # Test adding headers one by one
        progressive_headers = minimal_headers.copy()
        
        header_additions = [
            ('Accept', 'application/json, text/plain, */*'),
            ('Origin', self.config['gainsight']['url']),
            ('Referer', f"{self.config['gainsight']['url']}/v1/ui/customersuccess360"),
            ('User-Agent', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'),
            ('X-Gs-Host', 'GAINSIGHT')
        ]
        
        for header_name, header_value in header_additions:
            progressive_headers[header_name] = header_value
            
            logger.info(f"📋 Testing with {header_name} added:")
            response = self.session.post(upload_url, files=files, data=form_data, headers=progressive_headers, timeout=60)
            logger.info(f"  Response: {response.status_code}")
            
            if response.status_code == 200:
                logger.info(f"  ✅ SUCCESS! Required headers: {list(progressive_headers.keys())}")
                self.log_success_details(response)
                return True
        
        return False

def main():
    print("🔄 EXACT BROWSER REQUEST MIMIC TEST")
    print("="*50)
    print("Mimicking the exact browser request that returned 200 OK")
    print()
    
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    
    try:
        tester = BrowserMimicUploadTest(config_file)
        
        # Test exact browser format
        success = tester.test_exact_browser_format()
        
        if not success:
            logger.info("\n🔍 Exact format failed, testing header variations...")
            success = tester.test_header_variations()
        
        if success:
            print("\n🎉 FOUND THE WORKING FORMAT!")
            print("Will update the migrator with the correct format.")
        else:
            print("\n❌ Still debugging needed.")
            print("Check logs for more details.")
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")

if __name__ == "__main__":
    main()
