#!/usr/bin/env python3
"""
FIXED Totango to Gainsight Attachment Migrator
This version fixes all the identified issues with attachment migration
"""

import requests
import json
import time
import logging
import os
import re
import base64
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from collections import defaultdict
from urllib.parse import urlparse, unquote

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("FixedAttachmentMigrator")

class FixedAttachmentMigrator:
    
    def __init__(self, config_file=None):
        self.config = self.load_config(config_file)
        self.session = requests.Session()
        self.results = {
            'success': 0,
            'failed': 0,
            'errors': [],
            'activity_types': {},
            'attachments_processed': 0,
            'attachments_uploaded': 0,
            'attachments_failed': 0,
            'download_attempts': 0,
            'download_successes': 0,
            'upload_attempts': 0,
            'upload_successes': 0
        }
        
        # Your existing mappings (keeping them as-is)
        self.touchpoint_id_mapping = {
            "12980f6d-797c-47da-ab58-78f3a3e5c954": "1I00J1INQCQ6GQ3T2MID7L1S4X8HPJ2OP95B",
            "16a15fcd-d735-49a7-8b14-ca279e6dba5a": "1I00J1INQCQ6GQ3T2MOPQWUV1NFURNPEK8VE",
            "30989b43-ff62-486c-b4db-f89c3106abba": "1I00J1INQCQ6GQ3T2MWULITYE9ASDFPQ2KD3",
            "38bb62d0-4b50-4a5f-906c-6edbfe4786db": "1I00J1INQCQ6GQ3T2MYNG8BC6SJCCN7SAPKC",
            "38ce7c4e-374f-42db-94e8-23ba48d4b1e3": "1I00J1INQCQ6GQ3T2MTA6WH5AKWS5JL0O87R",
            "6cc323e4-4155-48bf-964c-ec75ee70e96b": "1I00J1INQCQ6GQ3T2MGRUSTP2XZGZBXN5263",
            "7c285778-76be-4543-b8a1-9066ee5a1ad4": "1I00J1INQCQ6GQ3T2M0O5F16H63MEMNDN97J",
            "9165fe90-5bc2-480d-98b2-76f3926c8e23": "1I00J1INQCQ6GQ3T2MY27A9SQHEE253247F2",
            "a2d4c216-8d7b-4f24-9cdf-3c4bc0226f94": "1I00J1INQCQ6GQ3T2MJY0GQI8EZ9STP11VR2",
            "b1120b9a-85e9-4c2c-b7e7-9214ac3baaea": "1I00J1INQCQ6GQ3T2M3D94LFFY2R8UMNJXE0",
            "c5d4cd04-1224-4ad3-8696-d2fb70ea85be": "1I00J1INQCQ6GQ3T2MXT1PJXBF3KATW6QR4K",
            "cbd1f1f4-229a-4e1b-b4a3-a091e90932cc": "1I00J1INQCQ6GQ3T2M6MLMI12JOYH1Z05ILP",
            "ceb3f746-7099-4083-bc18-7d941699c6b5": "1I00J1INQCQ6GQ3T2M9VQOCBJ5RVU2YW5V4B",
            "cefe339e-50d6-434d-a126-d66d652ce06c": "1I00J1INQCQ6GQ3T2MENNOYNKWB0ZEM5WII6",
            "ddf60bee-a1d2-4b03-b1bd-1f59eef0e214": "1I00J1INQCQ6GQ3T2M2M84A559MT05AWXZT7",
            "e43ae486-e0b8-4e2c-a8f3-fe17821b003d": "1I00J1INQCQ6GQ3T2MRYA87NSXMM46FZCUZH",
            "e4b6a6cf-0833-4a60-aac2-52759e73e9ab": "1I00J1INQCQ6GQ3T2M16QWTIDVSRJTOA8219",
            "eb5d8037-0f63-452e-8007-977e91e061b5": "1I00J1INQCQ6GQ3T2MZKWMBM3ZATASUU2VFS",
            "ed358b0e-5cad-42e6-80be-04df0db7dc2c": "1I00J1INQCQ6GQ3T2MQN0KLL5SPTQZYCWWG6",
            "f9f8cc62-1436-4f96-afe7-ef77a2ff18ed": "1I00J1INQCQ6GQ3T2MNUINIM89ZGC48DOXQR"
        }
        
        self.flow_type_id_mapping = {
            "renewal": "1I00EBMOY66ROGCBY6844WMEDZF8B8RXHTP1",
            "support": "1I00EBMOY66ROGCBY66HJSD9I0AG8E8Q1A0D",
            "upsell": "1I00EBMOY66ROGCBY6J9O3U5YYU9F5NDTN2X",
            "escalation": "1I00EBMOY66ROGCBY6L0K6AGIJF5FPS2BATD",
            "onboarding_101": "1I00EBMOY66ROGCBY63I51PAXG5OJRPK37AQ",
            "adoption": "1I00EBMOY66ROGCBY6IJJ2GVGY81F62M23Z6",
            "risk_1560979263618": "1I00EBMOY66ROGCBY6YKHPSNQCJ382C3YQQU",
            "product_transition_1630456786595": "1I00EBMOY66ROGCBY6F8BZABGHFA7Z1OOYW5",
            "intelligence_1561140678082": "1I00EBMOY66ROGCBY6H0LHB0SGQLAU3N58O4",
            "services_1631204797082": "1I00EBMOY66ROGCBY650ON17KW7GYGD2H5ZY",
            "inbound_1631240727463": "1I00EBMOY66ROGCBY6YAF5US4GK95QEO5ZFU",
            "design_partner_1635451768576": "1I00EBMOY66ROGCBY6F8NQVJY43GPGH1XTD8",
            "nps_1654203738879": "1I00EBMOY66ROGCBY68KLFLZ3KPM5GGP9YUH",
            "business_review_1628207371592": "1I00EBMOY66ROGCBY6EOK9JO7C3HUUM9O5F7",
            "journey_1713548309375": "1I00EBMOY66ROGCBY6QL90GOMJA5GILCWWLT",
            "lifecycle_1714094340032": "1I00EBMOY66ROGCBY63E4BLUKMID71508EXR"
        }
    
    def load_config(self, config_file):
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r') as f:
                return json.load(f)
        return {}
    
    # ============================================================================
    # FIXED ATTACHMENT DOWNLOAD METHODS
    # ============================================================================
    
    def download_totango_file_fixed(self, attachment: Dict) -> Optional[bytes]:
        """FIXED download method that tries multiple approaches"""
        asset_id = attachment.get('totango_id', '')
        download_path = attachment.get('download_path', '')
        file_name = attachment.get('name', 'attachment')
        
        logger.info(f"📥 DOWNLOADING: {file_name} (ID: {asset_id})")
        self.results['download_attempts'] += 1
        
        # Method 1: Try with current JWT token and assets-proxy
        logger.info("🔄 Method 1: JWT token with assets-proxy")
        file_content = self._try_jwt_download(download_path)
        if file_content:
            logger.info(f"✅ Method 1 SUCCESS: {len(file_content)} bytes")
            self.results['download_successes'] += 1
            return file_content
        
        # Method 2: Try direct path with session
        logger.info("🔄 Method 2: Direct path with session")
        file_content = self._try_direct_download(download_path)
        if file_content:
            logger.info(f"✅ Method 2 SUCCESS: {len(file_content)} bytes")
            self.results['download_successes'] += 1
            return file_content
        
        # Method 3: Try asset metadata API
        logger.info("🔄 Method 3: Asset metadata API")
        file_content = self._try_metadata_download(asset_id)
        if file_content:
            logger.info(f"✅ Method 3 SUCCESS: {len(file_content)} bytes")
            self.results['download_successes'] += 1
            return file_content
        
        # Method 4: Try alternative proxy format
        logger.info("🔄 Method 4: Alternative proxy format")
        file_content = self._try_alternative_proxy(download_path)
        if file_content:
            logger.info(f"✅ Method 4 SUCCESS: {len(file_content)} bytes")
            self.results['download_successes'] += 1
            return file_content
        
        logger.error(f"❌ All download methods failed for {file_name}")
        return None
    
    def _try_jwt_download(self, download_path: str) -> Optional[bytes]:
        """Try JWT token download with assets-proxy"""
        try:
            token = self.config.get('totango', {}).get('jwt_token', '')
            if not token:
                return None
            
            path_parts = download_path.strip('/').split('/')
            if len(path_parts) < 2:
                return None
            
            service_id = path_parts[0]
            file_path = '/'.join(path_parts[1:])
            
            # Use the format from your successful example
            download_url = f"https://assets-proxy.totango.com/api/v2/assets/{service_id}/{file_path}?token={token}"
            
            response = self.session.get(download_url, timeout=60)
            
            if response.status_code == 200:
                return response.content
            else:
                logger.debug(f"JWT download failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.debug(f"JWT download error: {e}")
            return None
    
    def _try_direct_download(self, download_path: str) -> Optional[bytes]:
        """Try direct download with session cookies"""
        try:
            direct_url = f"{self.config['totango']['url']}{download_path}"
            
            response = self.session.get(
                direct_url,
                headers=self.config['totango']['headers'],
                timeout=60
            )
            
            if response.status_code == 200:
                return response.content
            else:
                logger.debug(f"Direct download failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.debug(f"Direct download error: {e}")
            return None
    
    def _try_metadata_download(self, asset_id: str) -> Optional[bytes]:
        """Try getting download URL from asset metadata"""
        try:
            metadata_url = f"{self.config['totango']['url']}/t01/mend/api/v2/assets/{asset_id}"
            
            response = self.session.get(
                metadata_url,
                headers=self.config['totango']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                download_url = result.get('downloadUrl') or result.get('url')
                
                if download_url:
                    file_response = self.session.get(
                        download_url,
                        headers=self.config['totango']['headers'],
                        timeout=60
                    )
                    
                    if file_response.status_code == 200:
                        return file_response.content
            
            return None
            
        except Exception as e:
            logger.debug(f"Metadata download error: {e}")
            return None
    
    def _try_alternative_proxy(self, download_path: str) -> Optional[bytes]:
        """Try alternative proxy format without token"""
        try:
            path_parts = download_path.strip('/').split('/')
            if len(path_parts) < 2:
                return None
            
            service_id = path_parts[0]
            file_path = '/'.join(path_parts[1:])
            
            proxy_url = f"https://assets-proxy.totango.com/api/v2/assets/{service_id}/{file_path}"
            
            response = self.session.get(
                proxy_url,
                headers=self.config['totango']['headers'],
                timeout=60
            )
            
            if response.status_code == 200:
                return response.content
            else:
                logger.debug(f"Alternative proxy failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.debug(f"Alternative proxy error: {e}")
            return None
    
    # ============================================================================
    # FIXED GAINSIGHT UPLOAD METHODS
    # ============================================================================
    
    def upload_to_gainsight_fixed(self, attachment: Dict, file_content: bytes, activity_id: str) -> Optional[str]:
        """FIXED upload method using the exact working format"""
        try:
            file_name = attachment.get('name', 'attachment')
            file_extension = attachment.get('extension', '')
            
            logger.info(f"📤 UPLOADING: {file_name} to activity {activity_id}")
            logger.info(f"📊 File size: {len(file_content):,} bytes")
            
            self.results['upload_attempts'] += 1
            
            # Prepare multipart file
            files = {
                'file': (file_name, file_content, self._get_mime_type(file_extension))
            }
            
            # EXACT format that works (from your successful example)
            form_data = {
                'entityId': activity_id,  # This is CRITICAL - the activity ID
                'contexts': json.dumps([{
                    'id': self.config['gainsight']['company_id'],
                    'base': True,
                    'obj': 'Company',
                    'lbl': self.config['gainsight']['company_name'],
                    'eid': None,
                    'eobj': 'Account',
                    'eurl': None,
                    'esys': 'SALESFORCE',
                    'dsp': True
                }]),
                'source': 'C360',
                'user': json.dumps({
                    'id': self.config['gainsight']['user_id'],
                    'obj': 'User',
                    'name': self.config['gainsight']['user_name'],
                    'email': self.config['gainsight']['user_email'],
                    'eid': None,
                    'eobj': 'User',
                    'epp': None,
                    'esys': 'SALESFORCE',
                    'sys': 'GAINSIGHT',
                    'pp': ''
                }),
                'type': 'DEFAULT'
            }
            
            # EXACT headers that work
            headers = {
                'Accept': 'application/json, text/plain, */*',
                'Cookie': self.config['gainsight']['headers'].get('Cookie', ''),
                'Origin': self.config['gainsight']['url'],
                'Referer': f"{self.config['gainsight']['url']}/v1/ui/customersuccess360?cid={self.config['gainsight']['company_id']}",
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-Gs-Host': 'GAINSIGHT'
            }
            
            # Upload URL
            upload_url = f"{self.config['gainsight']['url']}/v1/ant/attachments"
            
            response = self.session.post(
                upload_url,
                files=files,
                data=form_data,
                headers=headers,
                timeout=120
            )
            
            logger.info(f"📥 Upload response: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    
                    if result.get('result') and result.get('data'):
                        attachment_data = result['data']
                        attachment_id = attachment_data.get('id')
                        attachment_url = attachment_data.get('url', '')
                        
                        if attachment_id:
                            logger.info(f"✅ UPLOAD SUCCESS!")
                            logger.info(f"  📄 Attachment ID: {attachment_id}")
                            logger.info(f"  📄 Name: {attachment_data.get('name')}")
                            logger.info(f"  📄 Size: {attachment_data.get('size')} bytes")
                            logger.info(f"  📄 URL: {attachment_url[:100]}...")
                            
                            self.results['attachments_uploaded'] += 1
                            self.results['upload_successes'] += 1
                            return attachment_id
                        else:
                            logger.error(f"❌ No attachment ID in response")
                    else:
                        logger.error(f"❌ Unexpected response structure: {result}")
                        
                except json.JSONDecodeError as e:
                    logger.error(f"❌ Invalid JSON response: {e}")
                    logger.error(f"Response text: {response.text[:500]}")
            else:
                logger.error(f"❌ Upload failed: {response.status_code}")
                if response.status_code == 400:
                    logger.error("Bad Request - Check field mapping")
                elif response.status_code == 401:
                    logger.error("Unauthorized - Check authentication")
                elif response.status_code == 403:
                    logger.error("Forbidden - Insufficient permissions")
                
                logger.error(f"Response: {response.text[:500]}")
            
            self.results['attachments_failed'] += 1
            return None
            
        except Exception as e:
            logger.error(f"❌ Upload error: {e}")
            self.results['attachments_failed'] += 1
            return None
    
    def _get_mime_type(self, extension: str) -> str:
        """Get MIME type for file extension"""
        extension = extension.lower().lstrip('.')
        
        mime_types = {
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt': 'application/vnd.ms-powerpoint',
            'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'gif': 'image/gif',
            'txt': 'text/plain',
            'csv': 'text/csv',
            'zip': 'application/zip'
        }
        
        return mime_types.get(extension, 'application/octet-stream')
    
    # ============================================================================
    # ATTACHMENT PROCESSING METHODS  
    # ============================================================================
    
    def extract_totango_attachments(self, activity: Dict) -> List[Dict]:
        """Extract attachments from Totango activity"""
        attachments = []
        
        try:
            properties = activity.get('properties', {})
            assets = properties.get('assets', [])
            
            if not assets:
                return attachments
            
            logger.info(f"🔍 Found {len(assets)} assets in activity")
            
            for asset_str in assets:
                try:
                    if isinstance(asset_str, str):
                        asset = json.loads(asset_str)
                    else:
                        asset = asset_str
                    
                    attachment_info = self._parse_totango_asset(asset)
                    if attachment_info:
                        attachments.append(attachment_info)
                        self.results['attachments_processed'] += 1
                        
                except json.JSONDecodeError as e:
                    logger.error(f"❌ Failed to parse asset JSON: {e}")
                except Exception as e:
                    logger.error(f"❌ Error processing asset: {e}")
            
            logger.info(f"✅ Extracted {len(attachments)} valid attachments")
            
        except Exception as e:
            logger.error(f"❌ Error extracting attachments: {e}")
        
        return attachments
    
    def _parse_totango_asset(self, asset: Dict) -> Optional[Dict]:
        """Parse individual Totango asset"""
        try:
            asset_type = asset.get('asset_type', '')
            name = asset.get('name', 'Attachment')
            asset_url = asset.get('asset_url', '')
            download_path = asset.get('downloadPath', '')
            extension = asset.get('extension', '')
            asset_id = asset.get('id', '')
            
            if asset_type == 'link' and asset_url:
                # External link
                return {
                    'type': 'link',
                    'name': name,
                    'url': asset_url,
                    'extension': extension,
                    'totango_id': asset_id,
                    'needs_upload': False
                }
            elif asset_type == 'file' and download_path:
                # File that needs to be downloaded and uploaded
                return {
                    'type': 'file',
                    'name': name,
                    'url': '',
                    'extension': extension,
                    'totango_id': asset_id,
                    'download_path': download_path,
                    'needs_upload': True
                }
            else:
                logger.warning(f"⚠️ Skipping asset: {name} (type: {asset_type})")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error parsing asset: {e}")
            return None
    
    def process_attachments_for_activity(self, activity: Dict, activity_id: str) -> List[Dict]:
        """Process all attachments for an activity AFTER it's created"""
        gainsight_attachments = []
        
        try:
            # Extract attachments from Totango
            totango_attachments = self.extract_totango_attachments(activity)
            
            if not totango_attachments:
                logger.info("ℹ️ No attachments found for this activity")
                return gainsight_attachments
            
            logger.info(f"🔗 Processing {len(totango_attachments)} attachments for activity {activity_id}")
            
            for attachment in totango_attachments:
                try:
                    if not attachment.get('needs_upload', False):
                        # For links, we could add them to the note content instead
                        logger.info(f"🔗 Skipping link: {attachment['name']}")
                        continue
                    
                    # Download file from Totango
                    file_content = self.download_totango_file_fixed(attachment)
                    
                    if not file_content:
                        logger.error(f"❌ Failed to download: {attachment['name']}")
                        continue
                    
                    # Upload to Gainsight with activity ID
                    attachment_id = self.upload_to_gainsight_fixed(attachment, file_content, activity_id)
                    
                    if attachment_id:
                        gainsight_attachments.append({
                            'id': attachment_id,
                            'name': attachment['name'],
                            'seqId': attachment_id,
                            'published': True,
                            'removed': False
                        })
                        logger.info(f"✅ Successfully processed: {attachment['name']}")
                    else:
                        logger.error(f"❌ Failed to upload: {attachment['name']}")
                        
                except Exception as e:
                    logger.error(f"❌ Error processing attachment {attachment.get('name', 'unknown')}: {e}")
                    continue
            
            logger.info(f"✅ Processed {len(gainsight_attachments)} attachments successfully")
            
        except Exception as e:
            logger.error(f"❌ Error processing attachments: {e}")
        
        return gainsight_attachments
    
    # ============================================================================
    # EXISTING METHODS (Keeping your working activity migration logic)
    # ============================================================================
    
    def get_totango_activities(self, account_ids: List[str]) -> List[Dict]:
        """Get activities from Totango (your existing method)"""
        logger.info(f"Extracting activities from {len(account_ids)} Totango accounts...")
        
        all_activities = []
        
        for account_id in account_ids:
            try:
                url = f"{self.config['totango']['url']}/t01/mend/api/v2/events/"
                params = {
                    'account_id': account_id,
                    'include_formatting': 'true'
                }
                
                response = self.session.get(
                    url,
                    headers=self.config['totango']['headers'],
                    params=params,
                    timeout=30
                )
                
                if response.status_code == 200:
                    events = response.json()
                    
                    meeting_events = []
                    for event in events:
                        properties = event.get('properties', {})
                        if 'meeting_type' in properties and properties['meeting_type']:
                            event['sourceAccountId'] = account_id
                            meeting_events.append(event)
                    
                    all_activities.extend(meeting_events)
                    logger.info(f"Account {account_id}: {len(meeting_events)} activities")
                    
                else:
                    logger.error(f"Failed for account {account_id}: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"Error with account {account_id}: {e}")
                
        logger.info(f"Total activities extracted: {len(all_activities)}")
        return all_activities
    
    def get_gainsight_users(self) -> Dict[str, Dict]:
        """Get Gainsight users (your existing method)"""
        logger.info("Loading Gainsight users...")
        
        users = {}
        try:
            all_users = []
            page_number = 1
            
            while True:
                payload = {
                    'limit': 50,
                    'pageNumber': page_number,
                    'searchString': '',
                    'clause': None,
                    'fields': ['Email', 'Gsid', 'Name']
                }
                
                response = self.session.post(
                    f"{self.config['gainsight']['url']}/v1/dataops/gdm/list?object=GsUser",
                    json=payload,
                    headers=self.config['gainsight']['headers'],
                    timeout=30
                )
                
                if response.status_code != 200:
                    break
                    
                users_data = response.json().get('data', {}).get('data', [])
                if not users_data:
                    break
                    
                all_users.extend(users_data)
                page_number += 1
                
            for user in all_users:
                email = user.get('Email', '').lower()
                if email:
                    users[email] = {
                        'id': user.get('Gsid', ''),
                        'name': user.get('Name', ''),
                        'email': user.get('Email', '')
                    }
                    
            logger.info(f"Loaded {len(users)} users")
            
        except Exception as e:
            logger.error(f"Failed to load users: {e}")
            
        return users
    
    def get_gainsight_activity_types(self, company_id: str) -> Dict[str, str]:
        """Get Gainsight activity types (your existing method)"""
        logger.info("Loading activity types...")
        
        try:
            url = f"{self.config['gainsight']['url']}/v1/ant/forms"
            params = {
                'context': 'Company',
                'contextId': company_id,
                'showHidden': 'false'
            }
            
            response = self.session.get(
                url,
                params=params,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data and 'activityTypes' in data['data']:
                    activity_types = data['data']['activityTypes']
                    
                    type_mapping = {}
                    for activity_type in activity_types:
                        name = activity_type.get('name', '').upper()
                        type_id = activity_type.get('id', '')
                        if name and type_id:
                            type_mapping[name] = type_id
                    
                    logger.info(f"Successfully loaded {len(type_mapping)} activity types")
                    self.results['activity_types'] = type_mapping
                    
                    return type_mapping
                    
            logger.error(f"Failed to load activity types: {response.status_code}")
            
        except Exception as e:
            logger.error(f"Exception loading activity types: {e}")
            
        return {}
    
    def transform_activity(self, activity: Dict, users: Dict, activity_types: Dict) -> Optional[Dict]:
        """Transform Totango activity to Gainsight format (your existing method with mappings)"""
        try:
            properties = activity.get('properties', {})
            
            subject = self._extract_subject(activity)
            content = self._extract_content(activity)
            meeting_type_id = properties.get('meeting_type', '')
            timestamp = activity.get('timestamp', '')
            
            activity_type_id = self._map_activity_type(meeting_type_id, activity_types)
            if not activity_type_id:
                return None
                
            activity_date = self._format_timestamp(timestamp)
            author_info = self._extract_author_info(activity, users)
            
            # Apply your mappings
            touchpoint_reason_id = self._map_touchpoint_reason(activity)
            flow_type_id = self._map_flow_type(activity)
            
            custom_fields = {
                'internalAttendees': [
                    {
                        'id': author_info['id'],
                        'obj': 'User',
                        'name': author_info['name'],
                        'email': author_info['email'],
                        'eid': None,
                        'eobj': 'User',
                        'epp': None,
                        'esys': 'SALESFORCE',
                        'sys': 'GAINSIGHT',
                        'pp': ''
                    }
                ],
                'externalAttendees': []
            }
            
            if touchpoint_reason_id:
                custom_fields['Ant__Touchpoint_Reason__c'] = touchpoint_reason_id
            
            if flow_type_id:
                custom_fields['Ant__Flow_Type__c'] = flow_type_id
            
            payload = {
                'lastModifiedByUser': {
                    'gsId': author_info['id'],
                    'name': author_info['name'],
                    'eid': None,
                    'esys': None,
                    'pp': ''
                },
                'note': {
                    'customFields': custom_fields,
                    'type': self._get_activity_type_name_from_id(activity_type_id, activity_types),
                    'subject': subject,
                    'activityDate': activity_date,
                    'content': content,
                    'plainText': self._strip_html(content),
                    'trackers': None
                },
                'mentions': [],
                'relatedRecords': {},
                'meta': {
                    'activityTypeId': activity_type_id,
                    'ctaId': None,
                    'source': 'C360',
                    'hasTask': False,
                    'emailSent': False,
                    'systemType': 'GAINSIGHT',
                    'notesTemplateId': None
                },
                'author': {
                    'id': author_info['id'],
                    'obj': 'User',
                    'name': author_info['name'],
                    'email': author_info['email'],
                    'eid': None,
                    'eobj': 'User',
                    'epp': None,
                    'esys': 'SALESFORCE',
                    'sys': 'GAINSIGHT',
                    'pp': ''
                },
                'syncedToSFDC': False,
                'tasks': [],
                'attachments': [],  # Will be populated after upload
                'contexts': [
                    {
                        'id': self.config['gainsight']['company_id'],
                        'base': True,
                        'obj': 'Company',
                        'lbl': self.config['gainsight']['company_name'],
                        'eid': None,
                        'eobj': 'Account',
                        'eurl': None,
                        'esys': 'SALESFORCE',
                        'dsp': True
                    }
                ]
            }
            
            return payload
            
        except Exception as e:
            logger.error(f"Transform error: {e}")
            return None
    
    # Helper methods for transform_activity
    def _extract_subject(self, activity: Dict) -> str:
        properties = activity.get('properties', {})
        
        for field in ['subject', 'name', 'display_name', 'title']:
            if field in properties and properties[field]:
                return str(properties[field])[:200]
                
        activity_type = activity.get('type', 'Activity')
        return f"Totango: {activity_type.replace('_', ' ').title()}"
    
    def _extract_content(self, activity: Dict) -> str:
        if 'note_content' in activity:
            note_content = activity['note_content']
            if isinstance(note_content, dict) and 'text' in note_content:
                text_content = note_content['text']
                if text_content and text_content.strip():
                    if text_content.startswith('<') and ('</p>' in text_content or '</div>' in text_content):
                        return text_content
                    else:
                        return f"<p>{text_content}</p>"

        properties = activity.get('properties', {})
        for field in ['description', 'content', 'message', 'body', 'subject']:
            if field in properties and properties[field]:
                content = str(properties[field])
                if content.strip():
                    return f"<p>{content}</p>" if not content.startswith('<') else content
                    
        activity_type = activity.get('type', 'activity')
        return f"<p>Totango Activity: {activity_type.replace('_', ' ')}</p>"
    
    def _extract_author_info(self, activity: Dict, users: Dict) -> Dict:
        enriched_users = activity.get('enrichedUsers', [])
        if enriched_users and isinstance(enriched_users, list):
            user = enriched_users[0]
            if isinstance(user, dict):
                email = user.get('email', '').lower()
                if email and email in users:
                    return users[email]
                    
        author = activity.get('author', {})
        if isinstance(author, dict):
            email = author.get('email', '').lower()
            if email and email in users:
                return users[email]
                
        return {
            'id': self.config['gainsight']['user_id'],
            'name': self.config['gainsight']['user_name'],
            'email': self.config['gainsight']['user_email']
        }
    
    def _format_timestamp(self, timestamp) -> str:
        try:
            if isinstance(timestamp, str):
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            else:
                if timestamp > 10**10:
                    timestamp = timestamp / 1000
                dt = datetime.fromtimestamp(timestamp)
                
            return dt.isoformat()
        except:
            return datetime.now().isoformat()
    
    def _strip_html(self, html: str) -> str:
        if not html:
            return ""
        return re.sub(r'<[^>]*>', '', html)
    
    def _map_activity_type(self, meeting_type_id: str, gainsight_types: Dict[str, str]) -> str:
        if not meeting_type_id:
            return gainsight_types.get('EMAIL', list(gainsight_types.values())[0] if gainsight_types else '')
        
        mappings = {
            "email": "EMAIL",
            "call": "CALL", 
            "meeting": "MEETING",
            "note": "UPDATE",
            "update": "UPDATE",
            "feedback": "FEEDBACK",
            "slack": "SLACK",
            "inbound": "INBOUND"
        }
        
        clean_type = meeting_type_id.lower()
        for pattern, gainsight_type in mappings.items():
            if pattern in clean_type and gainsight_type in gainsight_types:
                return gainsight_types[gainsight_type]
        
        if 'UPDATE' in gainsight_types:
            return gainsight_types['UPDATE']
            
        return list(gainsight_types.values())[0] if gainsight_types else ''
    
    def _map_touchpoint_reason(self, totango_activity: Dict) -> Optional[str]:
        properties = totango_activity.get('properties', {})
        touchpoint_tags = properties.get('touchpoint_tags')
        
        if touchpoint_tags:
            if isinstance(touchpoint_tags, str):
                tag_ids = [touchpoint_tags]
            elif isinstance(touchpoint_tags, list):
                tag_ids = touchpoint_tags
            else:
                tag_ids = []
            
            for tag_id in tag_ids:
                if tag_id in self.touchpoint_id_mapping:
                    return self.touchpoint_id_mapping[tag_id]
        
        return None
    
    def _map_flow_type(self, totango_activity: Dict) -> Optional[str]:
        properties = totango_activity.get('properties', {})
        
        activity_type_id = properties.get('activity_type_id')
        if activity_type_id and activity_type_id in self.flow_type_id_mapping:
            return self.flow_type_id_mapping[activity_type_id]
        
        for field_name in ['flow_type', 'flow_type_id', 'type_id']:
            if field_name in properties:
                flow_type_id = properties[field_name]
                if flow_type_id in self.flow_type_id_mapping:
                    return self.flow_type_id_mapping[flow_type_id]
        
        return None
    
    def _get_activity_type_name_from_id(self, activity_type_id: str, activity_types: Dict[str, str]) -> str:
        for name, type_id in activity_types.items():
            if type_id == activity_type_id:
                return name
        return "Activity"
    
    def create_draft(self, payload: Dict) -> Optional[str]:
        """Create draft activity (your existing method)"""
        try:
            url = f"{self.config['gainsight']['url']}/v1/ant/v2/activity/drafts"
            
            response = self.session.post(
                url,
                json=payload,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                draft_data = response.json()
                draft_id = draft_data.get('data', {}).get('id')
                return draft_id
            else:
                logger.error(f"Draft failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Draft error: {e}")
            return None
    
    def create_timeline_entry(self, payload: Dict, draft_id: str) -> bool:
        """Create timeline entry (your existing method)"""
        try:
            url = f"{self.config['gainsight']['url']}/v1/ant/v2/activity"
            
            timeline_payload = payload.copy()
            timeline_payload['id'] = draft_id
            
            response = self.session.post(
                url,
                json=timeline_payload,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                return True
            else:
                logger.error(f"Timeline failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Timeline error: {e}")
            return False
    
    # ============================================================================
    # MAIN MIGRATION METHODS
    # ============================================================================
    
    def migrate_single_activity(self, activity: Dict, users: Dict, activity_types: Dict) -> Dict:
        """Migrate a single activity with FIXED attachment handling"""
        result = {
            'success': False,
            'activity_id': activity.get('id', 'unknown'),
            'account_id': activity.get('sourceAccountId', 'unknown'),
            'error': None,
            'attachments_processed': 0,
            'attachments_uploaded': 0
        }
        
        try:
            # Step 1: Create activity payload
            payload = self.transform_activity(activity, users, activity_types)
            if not payload:
                result['error'] = 'Transform failed'
                return result
                
            # Step 2: Create draft
            draft_id = self.create_draft(payload)
            if not draft_id:
                result['error'] = 'Draft failed'
                return result
                
            # Step 3: Create timeline entry
            if not self.create_timeline_entry(payload, draft_id):
                result['error'] = 'Timeline failed'
                return result
            
            # Step 4: FIXED attachment processing using the draft_id
            gainsight_attachments = self.process_attachments_for_activity(activity, draft_id)
            result['attachments_processed'] = len(gainsight_attachments)
            result['attachments_uploaded'] = len(gainsight_attachments)
            
            result['success'] = True
            
            if gainsight_attachments:
                logger.info(f"✅ Activity {draft_id} created with {len(gainsight_attachments)} attachments")
            else:
                logger.info(f"✅ Activity {draft_id} created (no attachments)")
                
        except Exception as e:
            result['error'] = str(e)
            logger.error(f"❌ Migration error: {e}")
            
        return result
    
    def migrate_all_activities(self, account_ids: List[str]):
        """MAIN migration method with FIXED attachment support"""
        logger.info("🚀 Starting FIXED attachment migration...")
        
        start_time = time.time()
        
        # Get data
        activities = self.get_totango_activities(account_ids)
        if not activities:
            logger.error("No activities found")
            return
            
        users = self.get_gainsight_users()
        company_id = self.config['gainsight']['company_id']
        activity_types = self.get_gainsight_activity_types(company_id)
        
        if not activity_types:
            logger.error("Failed to load activity types - aborting migration")
            return
        
        logger.info(f"📊 FIXED Migration Summary:")
        logger.info(f"  - Processing {len(activities)} activities")
        logger.info(f"  - With FIXED attachment download and upload")
        logger.info(f"  - Using proper activity ID for attachment linking")
        
        # Process activities
        for i, activity in enumerate(activities, 1):
            result = self.migrate_single_activity(activity, users, activity_types)
            
            if result['success']:
                self.results['success'] += 1
            else:
                self.results['failed'] += 1
                self.results['errors'].append(result)
            
            if i % 5 == 0:
                progress = (i / len(activities)) * 100
                logger.info(f"Progress: {i}/{len(activities)} ({progress:.1f}%)")
                logger.info(f"  Attachments: {self.results['attachments_uploaded']} uploaded / {self.results['attachments_failed']} failed")
            
            time.sleep(2.0)  # Rate limiting
            
        end_time = time.time()
        self._generate_report(end_time - start_time)
        self._save_results()
    
    def _generate_report(self, total_time: float):
        """Generate migration report"""
        total = self.results['success'] + self.results['failed']
        success_rate = (self.results['success'] / total * 100) if total > 0 else 0
        
        download_success_rate = 0
        if self.results['download_attempts'] > 0:
            download_success_rate = (self.results['download_successes'] / self.results['download_attempts']) * 100
        
        upload_success_rate = 0
        if self.results['upload_attempts'] > 0:
            upload_success_rate = (self.results['upload_successes'] / self.results['upload_attempts']) * 100
        
        report = f"""
🚀 FIXED TOTANGO TO GAINSIGHT MIGRATION REPORT
{'='*70}
📊 ACTIVITY STATISTICS:
    Total Activities: {total:,}
    ✅ Successful: {self.results['success']:,}
    ❌ Failed: {self.results['failed']:,}
    📈 Success Rate: {success_rate:.1f}%
    ⏱️ Total Time: {total_time:.1f} seconds

📎 ATTACHMENT STATISTICS:
    📁 Attachments Found: {self.results['attachments_processed']}
    📥 Download Attempts: {self.results['download_attempts']}
    ✅ Download Successes: {self.results['download_successes']} ({download_success_rate:.1f}%)
    📤 Upload Attempts: {self.results['upload_attempts']}
    ✅ Upload Successes: {self.results['upload_successes']} ({upload_success_rate:.1f}%)
    ❌ Final Failed: {self.results['attachments_failed']}

🎯 OVERALL ATTACHMENT SUCCESS: {self.results['attachments_uploaded']} files uploaded successfully
"""
        
        if self.results['errors']:
            report += f"""
❌ ERRORS (first 3):
"""
            for error in self.results['errors'][:3]:
                report += f"    {error.get('account_id', 'unknown')}: {error.get('error', 'unknown')}\n"
                
        report += f"""
{'='*70}
🎉 FIXED migration completed!
"""
        
        print(report)
    
    def _save_results(self):
        """Save migration results"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_dir = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram"
        
        results_file = os.path.join(base_dir, f"fixed_results_{timestamp}.json")
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        logger.info(f"Results saved: {results_file}")


def main():
    print("🚀 FIXED Totango to Gainsight Attachment Migrator")
    print("="*60)
    print("This version fixes all the download and upload issues!")
    print()
    
    account_ids = ['001b000003nwKk1AAE']  # ICICI Bank account
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    
    try:
        migrator = FixedAttachmentMigrator(config_file)
        migrator.migrate_all_activities(account_ids)
        logger.info("✅ FIXED migration completed!")
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")

if __name__ == "__main__":
    main()
