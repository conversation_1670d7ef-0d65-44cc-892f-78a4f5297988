#!/usr/bin/env python3
"""
Quick test of the EXACT Gainsight upload format
Using the exact format from the successful browser request
"""

import json
import requests
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("ExactFormatTest")

def test_exact_gainsight_format():
    """Test using the exact format from successful browser request"""
    
    # Load config
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    # Test file
    test_content = b"Test file using exact Gainsight format"
    test_filename = "exact_format_test.txt"
    
    logger.info("🧪 Testing EXACT Gainsight upload format...")
    logger.info(f"📤 File: {test_filename}, {len(test_content)} bytes")
    
    # Files for multipart
    files = {
        'file': (test_filename, test_content, 'text/plain')
    }
    
    # EXACT form data from successful browser request
    form_data = {
        'entityId': '1I004SG7RDV06L1HFJC34HQACAIX3BHGHH0G',  # Use example from browser
        'contexts': json.dumps([{
            'id': config['gainsight']['company_id'],
            'base': True,
            'obj': 'Company',
            'lbl': config['gainsight']['company_name'],
            'eid': None,
            'eobj': 'Account',
            'eurl': None,
            'esys': 'SALESFORCE',
            'dsp': True
        }]),
        'source': 'C360',
        'user': json.dumps({
            'id': config['gainsight']['user_id'],
            'obj': 'User',
            'name': config['gainsight']['user_name'],
            'email': config['gainsight']['user_email'],
            'eid': None,
            'eobj': 'User',
            'epp': None,
            'esys': 'SALESFORCE',
            'sys': 'GAINSIGHT',
            'pp': ''
        }),
        'type': 'DEFAULT'
    }
    
    # EXACT headers from successful browser request
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Cookie': config['gainsight']['headers']['Cookie'],
        'Origin': config['gainsight']['url'],
        'Referer': f"{config['gainsight']['url']}/v1/ui/customersuccess360?cid={config['gainsight']['company_id']}",
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'X-Gs-Host': 'GAINSIGHT'
    }
    
    # Upload URL
    upload_url = f"{config['gainsight']['url']}/v1/ant/attachments"
    
    logger.info(f"📡 URL: {upload_url}")
    logger.info(f"📋 Form data keys: {list(form_data.keys())}")
    logger.info(f"📋 Headers: {list(headers.keys())}")
    
    try:
        response = requests.post(
            upload_url,
            files=files,
            data=form_data,
            headers=headers,
            timeout=60
        )
        
        logger.info(f"📥 Response: {response.status_code}")
        
        if response.status_code == 200:
            logger.info("🎉 SUCCESS! Exact format works!")
            try:
                result = response.json()
                if result.get('data'):
                    attachment_data = result['data']
                    logger.info(f"✅ Attachment created:")
                    logger.info(f"  ID: {attachment_data.get('id')}")
                    logger.info(f"  Name: {attachment_data.get('name')}")
                    logger.info(f"  URL: {attachment_data.get('url', '')[:100]}...")
                    logger.info(f"  Size: {attachment_data.get('size')} bytes")
                else:
                    logger.info(f"📋 Response: {result}")
            except:
                logger.info(f"📋 Success response: {response.text[:200]}")
                
        elif response.status_code == 400:
            logger.error("❌ Bad Request (400) - Field mapping issue")
            logger.error(f"Response: {response.text}")
            
        elif response.status_code == 405:
            logger.error("❌ Method Not Allowed (405) - Header issue")
            
        else:
            logger.error(f"❌ Failed: {response.status_code}")
            logger.error(f"Response: {response.text[:200]}")
            
        return response.status_code == 200
        
    except Exception as e:
        logger.error(f"❌ Request error: {e}")
        return False

def main():
    print("🧪 EXACT GAINSIGHT FORMAT TEST")
    print("="*40)
    print("Testing the exact format from successful browser request")
    print()
    
    success = test_exact_gainsight_format()
    
    print("\n" + "="*50)
    if success:
        print("🎉 SUCCESS! The exact format works!")
        print("The migrator has been updated with this format.")
        print("Your attachment migration should now work perfectly!")
    else:
        print("❌ Still debugging needed.")
        print("Check the logs above for specific errors.")

if __name__ == "__main__":
    main()
