# 📎 Totango to Gainsight Attachment Migration Guide

## 🎯 Overview

This enhanced migration system adds comprehensive **attachment support** to your existing Totango → Gainsight migration. It handles:

- **📁 File Attachments**: PDFs, images, documents from Totango uploads
- **🔗 Link Attachments**: Gong recordings, Zoom links, Google Docs
- **📄 Embedded Links**: URLs found in note content
- **📧 Email Attachments**: Images and files from email activities

## 🚀 New Features

### ✅ What's New
- **Complete Attachment Processing**: Extracts, downloads, and uploads all attachment types
- **Smart Link Detection**: Finds embedded URLs in activity content
- **File Type Support**: PDFs, images, Office docs, archives, and more
- **Error Handling**: Robust error handling with detailed logging
- **Progress Tracking**: Real-time attachment processing statistics

### 📊 Attachment Statistics Tracking
- Attachments processed, uploaded, and failed
- Detailed error reporting for troubleshooting
- Progress indicators during migration

## 📁 File Structure

```
totango_ram/
├── enhanced_attachment_migrator.py    # ⭐ Main migration script with attachments
├── test_attachment_migration.py       # 🧪 Test script to validate setup
├── fixed_mapping_migrator.py          # 📋 Your existing migrator (backup)
├── migration_config.json              # ⚙️ Configuration file
├── attachments/                       # 📁 Working directory for temp files
└── migration.log                      # 📝 Detailed logs
```

## 🔧 Setup Instructions

### 1. Test Your Configuration
First, run the test script to validate your setup:

```bash
cd /Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram
python3 test_attachment_migration.py
```

This will:
- ✅ Validate your configuration
- 🔍 Analyze Totango data for attachments
- 🌐 Test Gainsight API connectivity
- 📊 Show attachment statistics

### 2. Run the Enhanced Migration

```bash
python3 enhanced_attachment_migrator.py
```

## 📋 How Attachment Mapping Works

### 🎯 Attachment Types & Processing

#### 1. **Link Attachments** (Direct URLs)
```json
{
  "asset_type": "link",
  "asset_url": "https://us-59737.app.gong.io/e/c-share/?tkn=...",
  "name": "Gong Recording"
}
```
- ✅ **Processed**: Added to activity content as clickable links
- 🔄 **Method**: Direct URL mapping (no download required)

#### 2. **File Attachments** (Uploaded Files)
```json
{
  "asset_type": "file", 
  "downloadPath": "/45606/account/001b000003nwKk1AAE/uploads/...",
  "name": "IBM February Deck.pdf"
}
```
- ✅ **Processed**: Downloaded from Totango → Uploaded to Gainsight
- 🔄 **Method**: 3-step process (extract → download → upload)

#### 3. **Embedded Links** (In Note Content)
```html
<p>Recording: https://zoom.us/rec/share/xyz123</p>
```
- ✅ **Processed**: Extracted from HTML/text content
- 🔄 **Method**: Regex pattern matching

## 🔄 Migration Process Flow

```
1. Extract Activity from Totango
   ↓
2. Parse Assets Array (JSON strings)
   ↓
3. Identify Attachment Types:
   ├─ Links (asset_url) → Add to content
   ├─ Files (downloadPath) → Download & Upload
   └─ Embedded → Extract from content
   ↓
4. Download Files from Totango
   ↓
5. Upload Files to Gainsight
   ↓
6. Create Activity with Attachments
```

## 📊 Real Data Examples

Based on your ICICI.json file, here are the attachment types found:

### 🎥 **Gong Recordings**
```
Name: "https://us-59737.app.gong.io/e/c-share/?tkn=56fedztc5xxlmrs4pmmiis1y"
Type: link
Status: ✅ Migrated as clickable link
```

### 📹 **Zoom Recordings** 
```
Name: "Zoom Meeting Recording with Passcode"
Type: link  
Status: ✅ Migrated as clickable link
```

### 📄 **PDF Documents**
```
Name: "IBM February Deck Monthly Deck (1).pdf"
Type: file
Status: ✅ Downloaded → Uploaded to Gainsight
```

### 🌐 **Google Docs**
```
Name: "Google Presentation Link"
Type: link
Status: ✅ Migrated as clickable link
```

## ⚙️ Configuration Requirements

### Required Gainsight Permissions
Your Gainsight user needs:
- ✅ **Attachment Upload**: `/v1/ant/attachments` access
- ✅ **Activity Creation**: Timeline creation permissions
- ✅ **Company Context**: Access to the target company

### Required Totango Access
- ✅ **API Access**: Event retrieval permissions
- ✅ **File Download**: Access to attachment download paths
- ✅ **Session Cookie**: Valid authentication

## 🧪 Testing & Validation

### Pre-Migration Tests
Run these commands to validate your setup:

```bash
# 1. Test configuration
python3 test_attachment_migration.py

# 2. Test with limited data first
# Edit enhanced_attachment_migrator.py line 1033:
# account_ids = ['001b000003nwKk1AAE']  # Start with one account
```

### Expected Output
```
🧪 ATTACHMENT MIGRATION TESTER
=================================================
1. CONFIGURATION VALIDATION
✅ Totango configuration is complete
✅ Gainsight configuration is complete

2. TOTANGO DATA EXTRACTION TEST
📊 TOTANGO DATA ANALYSIS:
  Total Events: 150
  Events with meeting_type: 45
  Events with assets: 12
  Total assets found: 18
  Asset types: {'link': 10, 'file': 8}

3. GAINSIGHT API CONNECTIVITY TEST
✅ Test upload successful! Attachment ID: abc123...

🎉 ALL TESTS PASSED! Ready for attachment migration!
```

## 📈 Migration Statistics

The enhanced migrator provides detailed statistics:

```
🚀 ENHANCED TOTANGO TO GAINSIGHT MIGRATION REPORT (WITH ATTACHMENTS)
===============================================================================
📊 STATISTICS:
    Total Activities: 45
    ✅ Successful: 43
    ❌ Failed: 2
    📈 Success Rate: 95.6%

📎 ATTACHMENT STATISTICS:
    ✅ Attachments Processed: 18
    ✅ Attachments Uploaded: 15
    ❌ Attachments Failed: 3

🎯 MAPPING STATISTICS:
    ✅ Touchpoint Reason Mappings Applied: 35
    ✅ Flow Type Mappings Applied: 40
```

## 🔧 Troubleshooting

### Common Issues & Solutions

#### ❌ "Upload failed: 413 Request Entity Too Large"
**Solution**: File too large for Gainsight
```python
# Add file size check before upload
if len(file_content) > 10 * 1024 * 1024:  # 10MB limit
    logger.warning(f"File too large: {attachment['name']}")
    return None
```

#### ❌ "Download failed: 404 Not Found"
**Solution**: Totango file path expired or moved
```python
# Check if downloadPath is still valid
# Files may have been archived or deleted
```

#### ❌ "No attachment ID in response"
**Solution**: Check Gainsight API response format
```python
# Verify the response structure matches expected format
logger.info(f"Full response: {response.json()}")
```

## 🎯 Key Improvements Over Original

| Feature | Original Migrator | Enhanced Migrator |
|---------|------------------|-------------------|
| **Attachments** | ❌ Empty array | ✅ Full processing |
| **Link Extraction** | ❌ None | ✅ Smart detection |
| **File Downloads** | ❌ None | ✅ Automatic |
| **Error Handling** | ⚠️ Basic | ✅ Comprehensive |
| **Progress Tracking** | ⚠️ Basic | ✅ Detailed stats |
| **Validation** | ❌ None | ✅ Pre-flight tests |

## 📞 Support & Next Steps

### Immediate Actions
1. ✅ Run `test_attachment_migration.py` 
2. ✅ Review test results and fix any configuration issues
3. ✅ Run enhanced migrator on test account first
4. ✅ Verify attachments appear correctly in Gainsight
5. ✅ Scale to all accounts

### Future Enhancements
- 🔄 **Batch Processing**: Process multiple files simultaneously
- 📊 **Advanced Analytics**: Detailed attachment type analysis  
- 🔐 **Enhanced Security**: Virus scanning before upload
- 📱 **Mobile Support**: Handle mobile app attachments

---

**🎉 You now have a complete attachment migration system!** 

The enhanced migrator will handle all your Totango attachments automatically while maintaining all your existing touchpoint and flow type mappings.
