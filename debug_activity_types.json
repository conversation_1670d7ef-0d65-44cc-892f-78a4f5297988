{"data": {"fieldLimit": 200, "fieldsMap": {"0a657b4f-8ae9-4b1c-9b99-e200072dc714": {"id": "0a657b4f-8ae9-4b1c-9b99-e200072dc714", "deleted": false, "name": "priority", "label": "Priority", "description": "Default Priority", "dataType": "ARRAY", "fieldType": "SELECT", "scope": "SYSTEM", "hidden": true, "decimalPlaces": 0, "da": false, "new": true}, "d8e8c2a1-5539-4d78-9d9c-c7bf6e03ec79": {"id": "d8e8c2a1-5539-4d78-9d9c-c7bf6e03ec79", "deleted": false, "name": "Ant__Timeline_Status__c", "label": "Timeline Status", "dataType": "ARRAY", "fieldType": "DROPDOWN", "scope": "CUSTOM", "hidden": false, "picklistCategoryId": "1I00V4ALRX6A6ZOEQ06XUBLI5KWXFC6FJ0RD", "decimalPlaces": 0, "da": false, "new": true}, "7b911de6-b0e3-451b-877c-eafe44ec868f": {"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "deleted": false, "name": "subject", "label": "Subject", "description": "Text to capture context(heading) of the activity", "dataType": "STRING", "fieldType": "TEXT", "scope": "SYSTEM", "hidden": false, "decimalPlaces": 0, "da": false, "new": true}, "07901242-71c8-4284-a79d-7dc2e205aeeb": {"id": "07901242-71c8-4284-a79d-7dc2e205aeeb", "deleted": false, "name": "Ant__Commercials_discussed__c", "label": "Commercials discussed", "dataType": "BOOLEAN", "fieldType": "CHECKBOX", "scope": "CUSTOM", "hidden": false, "decimalPlaces": 0, "da": false, "new": true}, "af16f734-cb0d-4b14-9539-5b2203b5ae75": {"id": "af16f734-cb0d-4b14-9539-5b2203b5ae75", "deleted": false, "name": "ant__Exec_Present1543419438328", "label": "Exec Present", "dataType": "BOOLEAN", "fieldType": "CHECKBOX", "scope": "CUSTOM", "hidden": false, "decimalPlaces": 0, "da": false, "new": true}, "e379b72e-5cd9-4523-8d74-48d4e5cb7dd6": {"id": "e379b72e-5cd9-4523-8d74-48d4e5cb7dd6", "deleted": false, "name": "media_url", "label": "Media Url", "description": "Store Media Url of an activity", "dataType": "STRING", "fieldType": "TEXT", "scope": "SYSTEM", "hidden": true, "decimalPlaces": 0, "da": false, "new": true}, "12c7cb40-61ba-4bb3-a080-a7eefc588e82": {"id": "12c7cb40-61ba-4bb3-a080-a7eefc588e82", "deleted": false, "name": "ant__Meeting_Type1553613371043", "label": "Meeting Type", "dataType": "ARRAY", "fieldType": "DROPDOWN", "scope": "CUSTOM", "hidden": false, "picklistCategoryId": "1I00V4ALRX6A6ZOEQ0REFWTJK8NU8EVC9356", "decimalPlaces": 0, "da": false, "new": true}, "d8fff216-6ae0-483d-8a85-28e79a94bb54": {"id": "d8fff216-6ae0-483d-8a85-28e79a94bb54", "deleted": false, "name": "status", "label": "Default Closed Status", "description": "Default Closed Status", "dataType": "ARRAY", "fieldType": "SELECT", "scope": "SYSTEM", "hidden": true, "decimalPlaces": 0, "da": false, "new": true}, "c33a464e-9302-4dec-9306-c03478c6fdfe": {"id": "c33a464e-9302-4dec-9306-c03478c6fdfe", "deleted": false, "name": "ant__Escalation_Required1553613760001", "label": "Escalation Required", "dataType": "BOOLEAN", "fieldType": "CHECKBOX", "scope": "CUSTOM", "hidden": false, "decimalPlaces": 0, "da": false, "new": true}, "b4b976bf-d803-4a19-b025-0375ce917752": {"id": "b4b976bf-d803-4a19-b025-0375ce917752", "deleted": false, "name": "trackers", "label": "Trackers", "description": "Store Trackers as comma separated.", "dataType": "STRING", "fieldType": "TEXT", "scope": "SYSTEM", "hidden": true, "decimalPlaces": 0, "da": false, "new": true}, "e533d715-1c5e-4781-8952-f1b90e1deba9": {"id": "e533d715-1c5e-4781-8952-f1b90e1deba9", "deleted": false, "name": "Ant__externalid__c", "label": "externalid", "dataType": "STRING", "fieldType": "TEXT", "scope": "CUSTOM", "hidden": false, "decimalPlaces": 0, "da": false, "new": true}, "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7": {"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "deleted": false, "name": "note", "label": "Note", "description": "Text Area to capture the description of the activity", "dataType": "STRING", "fieldType": "RICH_TEXT_AREA", "scope": "SYSTEM", "hidden": false, "decimalPlaces": 0, "da": false, "new": true}, "158a82e0-3775-45e3-8b16-6df071df6aee": {"id": "158a82e0-3775-45e3-8b16-6df071df6aee", "deleted": false, "name": "internalAttendees", "label": "Internal Attendees", "description": "Internal users who were touched in the activity", "dataType": "STRING", "fieldType": "TAG", "scope": "SYSTEM", "hidden": false, "decimalPlaces": 0, "da": false, "new": true}, "52f29a31-50e8-4030-b886-74807ce12a75": {"id": "52f29a31-50e8-4030-b886-74807ce12a75", "deleted": false, "name": "Ant__Flow_Type__c", "label": "Flow Type", "dataType": "ARRAY", "fieldType": "DROPDOWN", "scope": "CUSTOM", "hidden": false, "picklistCategoryId": "1I00EL2BTVVSUAK9ZES52CEPN88ZMDD7ODDL", "decimalPlaces": 0, "da": false, "new": true}, "5caecad0-96ee-4740-87b6-8e68997ef631": {"id": "5caecad0-96ee-4740-87b6-8e68997ef631", "deleted": false, "name": "duration", "label": "Duration (in mins)", "description": "Time take for the activity in mins", "dataType": "NUMBER", "fieldType": "NUMBER", "scope": "SYSTEM", "hidden": false, "decimalPlaces": 0, "da": false, "new": true}, "b6a96e70-6414-453f-ac74-56e04bf07b86": {"id": "b6a96e70-6414-453f-ac74-56e04bf07b86", "deleted": false, "name": "createdDate", "label": "Created Date", "description": "Date on which activity was logged", "dataType": "DATETIME", "fieldType": "DATETIME", "scope": "SYSTEM", "hidden": true, "decimalPlaces": 0, "da": false, "new": true}, "7545a045-9f77-4c5b-a71e-59ce40ec6f78": {"id": "7545a045-9f77-4c5b-a71e-59ce40ec6f78", "deleted": false, "name": "linkTo", "label": "Link To", "description": "Account or Opportunity", "dataType": "STRING", "fieldType": "TEXT", "scope": "SYSTEM", "hidden": true, "decimalPlaces": 0, "da": false, "new": true}, "c70a85f8-2f7d-4e6b-9ace-fe1a0a887049": {"id": "c70a85f8-2f7d-4e6b-9ace-fe1a0a887049", "deleted": false, "name": "ant_type", "label": "Activity Type", "description": "Activity type", "dataType": "STRING", "fieldType": "TEXT", "scope": "SYSTEM", "hidden": true, "decimalPlaces": 0, "da": false, "new": true}, "49540cd4-1d0e-41bd-98d1-50188e6053af": {"id": "49540cd4-1d0e-41bd-98d1-50188e6053af", "deleted": false, "name": "ant__Sentiment_Trend1553613534660", "label": "Sentiment Trend", "dataType": "ARRAY", "fieldType": "DROPDOWN", "scope": "CUSTOM", "hidden": false, "picklistCategoryId": "1I00V4ALRX6A6ZOEQ0WH0C2E3NO27L4QGI72", "decimalPlaces": 0, "da": false, "new": true}, "4727df04-551c-4b8d-925b-f023ff070e71": {"id": "4727df04-551c-4b8d-925b-f023ff070e71", "deleted": false, "name": "externalAttendees", "label": "External Attendees", "description": "External contacts who were touched in the activity", "dataType": "STRING", "fieldType": "TAG", "scope": "SYSTEM", "hidden": false, "decimalPlaces": 0, "da": false, "new": true}, "1bf55c60-0930-412a-a1cd-2fc789571acd": {"id": "1bf55c60-0930-412a-a1cd-2fc789571acd", "deleted": false, "name": "milestoneType", "label": "Milestone Type", "description": "Milestone Type", "dataType": "ARRAY", "fieldType": "DROPDOWN", "scope": "SYSTEM", "hidden": true, "referTo": "SCRIBBLE", "picklistCategory": "MILESTONE", "decimalPlaces": 0, "da": true, "new": true}, "69adbaa2-5089-442b-9894-0fe27f6040bc": {"id": "69adbaa2-5089-442b-9894-0fe27f6040bc", "deleted": false, "name": "created<PERSON>y", "label": "Created By", "description": "User who logs the activity (automatically captured)", "dataType": "STRING", "fieldType": "TEXT", "scope": "SYSTEM", "hidden": true, "decimalPlaces": 0, "da": false, "new": true}, "9409051c-0e82-4ef3-be5d-ae88d0194d3b": {"id": "9409051c-0e82-4ef3-be5d-ae88d0194d3b", "deleted": false, "name": "Ant__Touchpoint_Reason__c", "label": "Touchpoint Reason", "dataType": "ARRAY", "fieldType": "DROPDOWN", "scope": "CUSTOM", "hidden": false, "picklistCategoryId": "1I00IURNPETEW0S7CAODIK73WMX9JA9GJVMQ", "decimalPlaces": 0, "da": false, "new": true}, "49a352ca-c7d5-4309-bf12-207c75fa55e3": {"id": "49a352ca-c7d5-4309-bf12-207c75fa55e3", "deleted": false, "name": "ant__Status1552512571338", "label": "Status", "dataType": "ARRAY", "fieldType": "DROPDOWN", "scope": "CUSTOM", "hidden": false, "picklistCategoryId": "1I00V4ALRX6A6ZOEQ0U6T64F2E1TY0CY7F2F", "decimalPlaces": 0, "da": false, "new": true}, "91a8415a-6be9-4716-a98b-ad2912261a06": {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "deleted": false, "name": "activityDate", "label": "Activity Date", "description": "Activity Date", "dataType": "DATE", "fieldType": "DATE", "scope": "SYSTEM", "hidden": false, "decimalPlaces": 0, "da": false, "new": true}, "0515560b-25c9-43f2-9991-f852961bcfdd": {"id": "0515560b-25c9-43f2-9991-f852961bcfdd", "deleted": false, "name": "external_source", "label": "External Source", "description": "Store external source of an activity", "dataType": "STRING", "fieldType": "TEXT", "scope": "SYSTEM", "hidden": true, "decimalPlaces": 0, "da": false, "new": true}}, "reportingCategories": [{"rcId": "b8f39a9a-4052-45e6-b5ac-edd5739ad0b8", "name": "Update", "label": "Update"}, {"rcId": "91a388f0-bf4b-4988-b2d4-c4b6e78cf717", "name": "Call", "label": "Call"}, {"rcId": "c54ca33c-27ea-4db8-a702-54fb4a954921", "name": "Meeting", "label": "Meeting"}, {"rcId": "84f99db3-6621-4071-98fe-9360a7bbcf96", "name": "Email", "label": "Email"}, {"rcId": "625887f1-9b14-46bf-bcf4-47bb3da2c320", "name": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>"}, {"rcId": "f027ba9a-f7c3-4ca3-9ca9-0aed85372457", "name": "Gong_call", "label": "Gong call"}, {"rcId": "8285115b-abc9-4c68-b8ba-dee020a23947", "name": "<PERSON><PERSON>ck", "label": "<PERSON><PERSON>ck"}, {"rcId": "554e35c6-405f-40a1-9398-3d9542bc069b", "name": "In_person_meeting", "label": "In person meeting "}, {"rcId": "82a406b2-dd1f-4ecb-beb2-d03b6479758e", "name": "Inbound", "label": "Inbound"}, {"rcId": "1ae81b72-d16f-4199-8e2e-51c8f5cc6c15", "name": "Internal_Note", "label": "Internal Note"}], "formFieldLimit": 15, "formLimit": 50, "antIntegrationEnabled": true, "hideReparentingConfirmationDialog": false, "activityTypes": [{"id": "dbd213f9-936b-47c9-86e6-b674fc515509", "createdBy": null, "createdDate": 1748944409546, "lastModifiedBy": null, "lastModifiedDate": 1748944409546, "sysModTimestamp": null, "deleted": false, "version": 11, "createdByUser": {"gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "formId": "dbd213f9-936b-47c9-86e6-b674fc515509", "name": "Update", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "UPDATE", "syncToSalesforce": false, "icon": "message", "fieldDefinitions": [{"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 1, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 2, "required": true, "displayName": "Note", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 3, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "158a82e0-3775-45e3-8b16-6df071df6aee", "displayOrder": 4, "required": false, "displayName": "Internal Attendees", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "4727df04-551c-4b8d-925b-f023ff070e71", "displayOrder": 5, "required": false, "displayName": "External Attendees", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "9409051c-0e82-4ef3-be5d-ae88d0194d3b", "displayOrder": 6, "required": false, "displayName": "Touchpoint Reason", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "52f29a31-50e8-4030-b886-74807ce12a75", "displayOrder": 7, "required": false, "displayName": "Flow Type", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "has logged a Update", "context": null, "contextId": null, "displayOrder": 1, "referenceFormId": null, "active": true, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "SYSTEM", "hidden": false, "showInFilter": true, "rcId": "b8f39a9a-4052-45e6-b5ac-edd5739ad0b8", "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#fff2f1", "new": false, "extContext": null, "extContextId": null, "extSystem": null}, {"id": "c1c47d97-b61c-4a5b-9b75-0bbc868a1216", "createdBy": null, "createdDate": 1748947370633, "lastModifiedBy": null, "lastModifiedDate": 1748947370633, "sysModTimestamp": null, "deleted": false, "version": 7, "createdByUser": {"gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "formId": "c1c47d97-b61c-4a5b-9b75-0bbc868a1216", "name": "Call", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "CALL", "syncToSalesforce": false, "icon": "call", "fieldDefinitions": [{"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 1, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 2, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "5caecad0-96ee-4740-87b6-8e68997ef631", "displayOrder": 3, "required": true, "displayName": "Duration (in mins)", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "158a82e0-3775-45e3-8b16-6df071df6aee", "displayOrder": 4, "required": true, "displayName": "Internal Attendees", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "4727df04-551c-4b8d-925b-f023ff070e71", "displayOrder": 5, "required": true, "displayName": "External Attendees", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 6, "required": true, "displayName": "Note", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "af16f734-cb0d-4b14-9539-5b2203b5ae75", "displayOrder": 7, "required": false, "displayName": "Exec Present", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "9409051c-0e82-4ef3-be5d-ae88d0194d3b", "displayOrder": 8, "required": false, "displayName": "Touchpoint Reason", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "52f29a31-50e8-4030-b886-74807ce12a75", "displayOrder": 9, "required": false, "displayName": "Flow Type", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "has logged a Call", "context": null, "contextId": null, "displayOrder": 2, "referenceFormId": null, "active": true, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "SYSTEM", "hidden": false, "showInFilter": true, "rcId": "91a388f0-bf4b-4988-b2d4-c4b6e78cf717", "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#d3ecfa", "new": false, "extContext": null, "extContextId": null, "extSystem": null}, {"id": "ace2ceef-a41c-4855-9660-a5ffc0d0babd", "createdBy": null, "createdDate": 1748947315122, "lastModifiedBy": null, "lastModifiedDate": 1748947315122, "sysModTimestamp": null, "deleted": false, "version": 10, "createdByUser": {"gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "formId": "ace2ceef-a41c-4855-9660-a5ffc0d0babd", "name": "Meeting", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "MEETING", "syncToSalesforce": false, "icon": "calendar-marked", "fieldDefinitions": [{"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 1, "required": true, "displayName": "Note", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 2, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 3, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "5caecad0-96ee-4740-87b6-8e68997ef631", "displayOrder": 4, "required": true, "displayName": "Duration (in mins)", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "158a82e0-3775-45e3-8b16-6df071df6aee", "displayOrder": 5, "required": true, "displayName": "Internal Attendees", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "4727df04-551c-4b8d-925b-f023ff070e71", "displayOrder": 6, "required": true, "displayName": "External Attendees", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "9409051c-0e82-4ef3-be5d-ae88d0194d3b", "displayOrder": 7, "required": false, "displayName": "Touchpoint Reason", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "52f29a31-50e8-4030-b886-74807ce12a75", "displayOrder": 8, "required": false, "displayName": "Flow Type", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "12c7cb40-61ba-4bb3-a080-a7eefc588e82", "displayOrder": 9, "required": false, "displayName": "Meeting Type", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "49540cd4-1d0e-41bd-98d1-50188e6053af", "displayOrder": 10, "required": false, "displayName": "Sentiment Trend", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "af16f734-cb0d-4b14-9539-5b2203b5ae75", "displayOrder": 11, "required": false, "displayName": "Exec Present", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "c33a464e-9302-4dec-9306-c03478c6fdfe", "displayOrder": 12, "required": false, "displayName": "Escalation Required", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "has logged a Meeting", "context": null, "contextId": null, "displayOrder": 3, "referenceFormId": null, "active": true, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "SYSTEM", "hidden": false, "showInFilter": true, "rcId": "c54ca33c-27ea-4db8-a702-54fb4a954921", "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#d4f0ed", "new": false, "extContext": null, "extContextId": null, "extSystem": null}, {"id": "c81eeccf-2aa1-45bc-be71-5377f148e1e9", "createdBy": null, "createdDate": 1748944394214, "lastModifiedBy": null, "lastModifiedDate": 1748944394214, "sysModTimestamp": null, "deleted": false, "version": 9, "createdByUser": {"gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "formId": "c81eeccf-2aa1-45bc-be71-5377f148e1e9", "name": "Email", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "EMAIL", "syncToSalesforce": false, "icon": "email", "fieldDefinitions": [{"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 1, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 2, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 3, "required": true, "displayName": "Email Body", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "158a82e0-3775-45e3-8b16-6df071df6aee", "displayOrder": 4, "required": true, "displayName": "Internal Recipients", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "4727df04-551c-4b8d-925b-f023ff070e71", "displayOrder": 5, "required": true, "displayName": "External Recipients", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "49a352ca-c7d5-4309-bf12-207c75fa55e3", "displayOrder": 6, "required": false, "displayName": "Status", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "9409051c-0e82-4ef3-be5d-ae88d0194d3b", "displayOrder": 7, "required": false, "displayName": "Touchpoint Reason", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "52f29a31-50e8-4030-b886-74807ce12a75", "displayOrder": 8, "required": false, "displayName": "Flow Type", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "has logged a Email", "context": null, "contextId": null, "displayOrder": 4, "referenceFormId": null, "active": true, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "SYSTEM", "hidden": false, "showInFilter": true, "rcId": "84f99db3-6621-4071-98fe-9360a7bbcf96", "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#f0e5fa", "new": false, "extContext": null, "extContextId": null, "extSystem": null}, {"id": "c514cd80-f44d-4e75-a8de-4dffcd7e20ce", "createdBy": null, "createdDate": 1532628747770, "lastModifiedBy": null, "lastModifiedDate": 1708666881807, "sysModTimestamp": null, "deleted": false, "version": 4, "createdByUser": {"gsId": "1P01E316G9DAPFOLE64YNOLNHZTYJY57SLOY", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6CK0BFATMABMMK186O8", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "formId": "c514cd80-f44d-4e75-a8de-4dffcd7e20ce", "name": "Milestone", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "MILESTONE", "syncToSalesforce": false, "icon": "activity-milestone", "fieldDefinitions": [{"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 1, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 2, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 3, "required": true, "displayName": "Note", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "1bf55c60-0930-412a-a1cd-2fc789571acd", "displayOrder": 4, "required": true, "displayName": "Milestone Type", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "4727df04-551c-4b8d-925b-f023ff070e71", "displayOrder": 5, "required": false, "displayName": "External Attendees", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "created a milestone", "context": "Global", "contextId": null, "displayOrder": 5, "referenceFormId": null, "active": true, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "SYSTEM", "hidden": false, "showInFilter": true, "rcId": null, "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#d3ecfa", "new": false, "extContext": "Global", "extContextId": null, "extSystem": null}, {"id": "d030f205-73b6-4228-b991-809f1b6e7870", "createdBy": null, "createdDate": 1542966512432, "lastModifiedBy": null, "lastModifiedDate": 1708666881807, "sysModTimestamp": null, "deleted": false, "version": 4, "createdByUser": {"gsId": "1P01E316G9DAPFOLE6M5L804855SJ1KSOEWA", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6CK0BFATMABMMK186O8", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "formId": "d030f205-73b6-4228-b991-809f1b6e7870", "name": "Renewal", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "RENEWAL_1542966512368", "syncToSalesforce": false, "icon": "activity-meeting", "fieldDefinitions": [{"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 1, "required": true, "displayName": "Note", "mandatory": true, "hidden": false, "disabled": true, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 2, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": true, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 3, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": true, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "has logged a Renewal", "context": "Global", "contextId": null, "displayOrder": 6, "referenceFormId": null, "active": true, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "CUSTOM", "hidden": false, "showInFilter": true, "rcId": "b8f39a9a-4052-45e6-b5ac-edd5739ad0b8", "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#fff2f1", "new": false, "extContext": "Global", "extContextId": null, "extSystem": "SALESFORCE"}, {"id": "4742d229-ad55-410f-9905-4568a258496e", "createdBy": null, "createdDate": 1542970847209, "lastModifiedBy": null, "lastModifiedDate": 1708666881807, "sysModTimestamp": null, "deleted": false, "version": 3, "createdByUser": {"gsId": "1P01E316G9DAPFOLE6M5L804855SJ1KSOEWA", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6CK0BFATMABMMK186O8", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "formId": "4742d229-ad55-410f-9905-4568a258496e", "name": "Risk", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "RISK_1542970847144", "syncToSalesforce": false, "icon": "activity-timer", "fieldDefinitions": [{"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 1, "required": true, "displayName": "Note", "mandatory": true, "hidden": false, "disabled": true, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 2, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": true, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 3, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": true, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "has logged a Risk", "context": "Global", "contextId": null, "displayOrder": 7, "referenceFormId": null, "active": true, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "CUSTOM", "hidden": false, "showInFilter": true, "rcId": "b8f39a9a-4052-45e6-b5ac-edd5739ad0b8", "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#fff2f1", "new": false, "extContext": "Global", "extContextId": null, "extSystem": "SALESFORCE"}, {"id": "17ede98d-875d-4efa-97a3-2c0d24023cff", "createdBy": null, "createdDate": 1611144705896, "lastModifiedBy": null, "lastModifiedDate": 1708666881807, "sysModTimestamp": null, "deleted": false, "version": 1, "createdByUser": {"gsId": "1P01E316G9DAPFOLE6WRG8M8TCTXN5DOODC4", "name": null, "pp": null, "eid": "1P01E316G9DAPFOLE6WRG8M8TCTXN5DOODC4", "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6WRG8M8TCTXN5DOODC4", "name": null, "pp": null, "eid": "1P01E316G9DAPFOLE6WRG8M8TCTXN5DOODC4", "esys": "SALESFORCE"}, "formId": "17ede98d-875d-4efa-97a3-2c0d24023cff", "name": "Renewal Conversation", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "RENEWAL_CONVERSATION_1611144705812", "syncToSalesforce": false, "icon": "activity-update", "fieldDefinitions": [{"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 1, "required": true, "displayName": "Note", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 2, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 3, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "07901242-71c8-4284-a79d-7dc2e205aeeb", "displayOrder": 4, "required": false, "displayName": "Commercials discussed", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "has logged a Renewal Conversation", "context": "Global", "contextId": null, "displayOrder": 8, "referenceFormId": null, "active": true, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "CUSTOM", "hidden": false, "showInFilter": true, "rcId": "b8f39a9a-4052-45e6-b5ac-edd5739ad0b8", "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#fff2f1", "new": false, "extContext": "Global", "extContextId": null, "extSystem": "SALESFORCE"}, {"id": "d96484df-ef5b-4b44-8e61-6a8d33bcafd7", "createdBy": null, "createdDate": 1613045830354, "lastModifiedBy": null, "lastModifiedDate": 1708666881807, "sysModTimestamp": null, "deleted": false, "version": 2, "createdByUser": {"gsId": "1P01E316G9DAPFOLE6WRG8M8TCTXN5DOODC4", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6WRG8M8TCTXN5DOODC4", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "formId": "d96484df-ef5b-4b44-8e61-6a8d33bcafd7", "name": "Timeline Status", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "TIMELINE_STATUS_1613045449222", "syncToSalesforce": false, "icon": "activity-task", "fieldDefinitions": [{"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 1, "required": true, "displayName": "Note", "mandatory": true, "hidden": false, "disabled": true, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 2, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": true, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 3, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": true, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "d8e8c2a1-5539-4d78-9d9c-c7bf6e03ec79", "displayOrder": 4, "required": false, "displayName": "Timeline Status", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "has logged a Timeline Status", "context": "Global", "contextId": null, "displayOrder": 9, "referenceFormId": null, "active": true, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "CUSTOM", "hidden": false, "showInFilter": true, "rcId": null, "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#fff2f1", "new": false, "extContext": "Global", "extContextId": null, "extSystem": "SALESFORCE"}, {"id": "8e0ba533-65dd-4f42-9410-165133f6b386", "createdBy": null, "createdDate": 1675478665590, "lastModifiedBy": null, "lastModifiedDate": 1708666881807, "sysModTimestamp": null, "deleted": false, "version": 1, "createdByUser": {"gsId": "", "name": null, "pp": null, "eid": "", "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "", "name": null, "pp": null, "eid": "", "esys": "SALESFORCE"}, "formId": "8e0ba533-65dd-4f42-9410-165133f6b386", "name": "Automated Program", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "AUTOMATED_PROGRAM", "syncToSalesforce": false, "icon": "activity-automated-program", "fieldDefinitions": [{"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 1, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 2, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 3, "required": true, "displayName": "Note", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "automated program", "context": "Global", "contextId": null, "displayOrder": 10, "referenceFormId": null, "active": false, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "SYSTEM", "hidden": false, "showInFilter": true, "rcId": null, "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#fff2f1", "new": false, "extContext": "Global", "extContextId": null, "extSystem": null}, {"id": "1467fe91-bdb8-499d-9254-a15ca0b6586b", "createdBy": null, "createdDate": 1748166819570, "lastModifiedBy": null, "lastModifiedDate": 1748166819570, "sysModTimestamp": null, "deleted": false, "version": 0, "createdByUser": {"gsId": "1P01E316G9DAPFOLE6V9Z586JRYFUW88XLGG", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6V9Z586JRYFUW88XLGG", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "formId": "1467fe91-bdb8-499d-9254-a15ca0b6586b", "name": "<PERSON><PERSON><PERSON>", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "FEEDBACK_1748166819532", "syncToSalesforce": false, "icon": "message", "fieldDefinitions": [{"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 1, "required": true, "displayName": "Note", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 2, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 3, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "9409051c-0e82-4ef3-be5d-ae88d0194d3b", "displayOrder": 4, "required": false, "displayName": "Touchpoint Reason", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "52f29a31-50e8-4030-b886-74807ce12a75", "displayOrder": 5, "required": false, "displayName": "Flow Type", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "has logged a Feedback", "context": null, "contextId": null, "displayOrder": 11, "referenceFormId": null, "active": true, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "CUSTOM", "hidden": false, "showInFilter": true, "rcId": "625887f1-9b14-46bf-bcf4-47bb3da2c320", "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#FFF2F1", "new": false, "extContext": null, "extContextId": null, "extSystem": null}, {"id": "b808fb71-efcb-4cdc-ae50-bef568fd09a1", "createdBy": null, "createdDate": 1748166873571, "lastModifiedBy": null, "lastModifiedDate": 1748166873571, "sysModTimestamp": null, "deleted": false, "version": 0, "createdByUser": {"gsId": "1P01E316G9DAPFOLE6V9Z586JRYFUW88XLGG", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6V9Z586JRYFUW88XLGG", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "formId": "b808fb71-efcb-4cdc-ae50-bef568fd09a1", "name": "Gong call ", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "GONG_CALL_1748166873534", "syncToSalesforce": false, "icon": "message", "fieldDefinitions": [{"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 1, "required": true, "displayName": "Note", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 2, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 3, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "9409051c-0e82-4ef3-be5d-ae88d0194d3b", "displayOrder": 4, "required": false, "displayName": "Touchpoint Reason", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "52f29a31-50e8-4030-b886-74807ce12a75", "displayOrder": 5, "required": false, "displayName": "Flow Type", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "5caecad0-96ee-4740-87b6-8e68997ef631", "displayOrder": 6, "required": false, "displayName": "Duration (in mins)", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "4727df04-551c-4b8d-925b-f023ff070e71", "displayOrder": 7, "required": false, "displayName": "External Attendees", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "158a82e0-3775-45e3-8b16-6df071df6aee", "displayOrder": 8, "required": false, "displayName": "Internal Attendees", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "has logged a Gong call ", "context": null, "contextId": null, "displayOrder": 12, "referenceFormId": null, "active": true, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "CUSTOM", "hidden": false, "showInFilter": true, "rcId": "f027ba9a-f7c3-4ca3-9ca9-0aed85372457", "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#FFF2F1", "new": false, "extContext": null, "extContextId": null, "extSystem": null}, {"id": "85395079-1786-4489-b9a1-d2df93006b1d", "createdBy": null, "createdDate": 1748166950570, "lastModifiedBy": null, "lastModifiedDate": 1748166950570, "sysModTimestamp": null, "deleted": false, "version": 0, "createdByUser": {"gsId": "1P01E316G9DAPFOLE6V9Z586JRYFUW88XLGG", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6V9Z586JRYFUW88XLGG", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "formId": "85395079-1786-4489-b9a1-d2df93006b1d", "name": "<PERSON><PERSON>ck", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "SLACK_1748166950531", "syncToSalesforce": false, "icon": "message", "fieldDefinitions": [{"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 1, "required": true, "displayName": "Note", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 2, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 3, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "9409051c-0e82-4ef3-be5d-ae88d0194d3b", "displayOrder": 4, "required": false, "displayName": "Touchpoint Reason", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "52f29a31-50e8-4030-b886-74807ce12a75", "displayOrder": 5, "required": false, "displayName": "Flow Type", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "has logged a Slack", "context": null, "contextId": null, "displayOrder": 13, "referenceFormId": null, "active": true, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "CUSTOM", "hidden": false, "showInFilter": true, "rcId": "8285115b-abc9-4c68-b8ba-dee020a23947", "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#FFF2F1", "new": false, "extContext": null, "extContextId": null, "extSystem": null}, {"id": "4bc21c0a-16db-4ce9-934b-872fab49de0b", "createdBy": null, "createdDate": 1748166989355, "lastModifiedBy": null, "lastModifiedDate": 1748166989355, "sysModTimestamp": null, "deleted": false, "version": 0, "createdByUser": {"gsId": "1P01E316G9DAPFOLE6V9Z586JRYFUW88XLGG", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6V9Z586JRYFUW88XLGG", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "formId": "4bc21c0a-16db-4ce9-934b-872fab49de0b", "name": "In person meeting ", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "IN_PERSON_MEETING_1748166989321", "syncToSalesforce": false, "icon": "message", "fieldDefinitions": [{"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 1, "required": true, "displayName": "Note", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 2, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 3, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "9409051c-0e82-4ef3-be5d-ae88d0194d3b", "displayOrder": 4, "required": false, "displayName": "Touchpoint Reason", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "52f29a31-50e8-4030-b886-74807ce12a75", "displayOrder": 5, "required": false, "displayName": "Flow Type", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "has logged a In person meeting ", "context": null, "contextId": null, "displayOrder": 14, "referenceFormId": null, "active": true, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "CUSTOM", "hidden": false, "showInFilter": true, "rcId": "554e35c6-405f-40a1-9398-3d9542bc069b", "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#FFF2F1", "new": false, "extContext": null, "extContextId": null, "extSystem": null}, {"id": "fb5abf7d-0c91-43e0-b101-5856f33327f4", "createdBy": null, "createdDate": 1748167109141, "lastModifiedBy": null, "lastModifiedDate": 1748167109141, "sysModTimestamp": null, "deleted": false, "version": 0, "createdByUser": {"gsId": "1P01E316G9DAPFOLE6V9Z586JRYFUW88XLGG", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6V9Z586JRYFUW88XLGG", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "formId": "fb5abf7d-0c91-43e0-b101-5856f33327f4", "name": "Inbound ", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "INBOUND_1748167109095", "syncToSalesforce": false, "icon": "message", "fieldDefinitions": [{"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 1, "required": true, "displayName": "Note", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 2, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 3, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "9409051c-0e82-4ef3-be5d-ae88d0194d3b", "displayOrder": 4, "required": false, "displayName": "Touchpoint Reason", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "52f29a31-50e8-4030-b886-74807ce12a75", "displayOrder": 5, "required": false, "displayName": "Flow Type", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "has logged a Inbound ", "context": null, "contextId": null, "displayOrder": 15, "referenceFormId": null, "active": true, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "CUSTOM", "hidden": false, "showInFilter": true, "rcId": "82a406b2-dd1f-4ecb-beb2-d03b6479758e", "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#FFF2F1", "new": false, "extContext": null, "extContextId": null, "extSystem": null}, {"id": "9b2d7fc8-5bfa-419b-bf32-5c0e25d2d46c", "createdBy": null, "createdDate": 1748445773566, "lastModifiedBy": null, "lastModifiedDate": 1748445773566, "sysModTimestamp": null, "deleted": false, "version": 2, "createdByUser": {"gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "formId": "9b2d7fc8-5bfa-419b-bf32-5c0e25d2d46c", "name": "Internal Note", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "INTERNAL_NOTE_1748445406995", "syncToSalesforce": false, "icon": "message", "fieldDefinitions": [{"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 1, "required": true, "displayName": "Note", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 2, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 3, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "9409051c-0e82-4ef3-be5d-ae88d0194d3b", "displayOrder": 4, "required": false, "displayName": "Touchpoint Reason", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "52f29a31-50e8-4030-b886-74807ce12a75", "displayOrder": 5, "required": false, "displayName": "Flow Type", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "has logged a Internal Note", "context": null, "contextId": null, "displayOrder": 16, "referenceFormId": null, "active": true, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "CUSTOM", "hidden": false, "showInFilter": true, "rcId": "1ae81b72-d16f-4199-8e2e-51c8f5cc6c15", "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#FFF2F1", "new": false, "extContext": null, "extContextId": null, "extSystem": null}, {"id": "3cd4991d-03d3-40db-bf45-83c60093fcbf", "createdBy": null, "createdDate": 1748445788613, "lastModifiedBy": null, "lastModifiedDate": 1748445788613, "sysModTimestamp": null, "deleted": false, "version": 2, "createdByUser": {"gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "lastModifiedByUser": {"gsId": "1P01E316G9DAPFOLE6SOOUG71XRMN5F3PLER", "name": null, "pp": null, "eid": null, "esys": "SALESFORCE"}, "formId": "3cd4991d-03d3-40db-bf45-83c60093fcbf", "name": "Web meeting", "tenantId": "590bb1b2-5f07-4569-8783-0a1b283f0761", "type": "WEB_MEETING_1748445440009", "syncToSalesforce": false, "icon": "message", "fieldDefinitions": [{"id": "d6c42b9c-f697-4ef9-bbb9-22cc58a75ee7", "displayOrder": 1, "required": true, "displayName": "Note", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "7b911de6-b0e3-451b-877c-eafe44ec868f", "displayOrder": 2, "required": true, "displayName": "Subject", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "91a8415a-6be9-4716-a98b-ad2912261a06", "displayOrder": 3, "required": true, "displayName": "Activity Date", "mandatory": true, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "9409051c-0e82-4ef3-be5d-ae88d0194d3b", "displayOrder": 4, "required": false, "displayName": "Touchpoint Reason", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}, {"id": "52f29a31-50e8-4030-b886-74807ce12a75", "displayOrder": 5, "required": false, "displayName": "Flow Type", "mandatory": false, "hidden": false, "disabled": false, "controllerFieldId": null, "decimalPlaces": 0, "defaultValue": null, "fieldAccess": "WRITE"}], "activityText": "has logged a Web meeting", "context": null, "contextId": null, "displayOrder": 17, "referenceFormId": null, "active": true, "activityUsageCount": 0, "draftUsageCount": 0, "scope": "CUSTOM", "hidden": false, "showInFilter": true, "rcId": "", "includeContexts": ["1P06CLSYGJWXIAJTCUASPM9Y5GO00D7WEHTQ", "1P06CLSYGJWXIAJTCUWODTJONF80YG3CKLJZ", "1P06CLSYGJWXIAJTCUQPULMCPVN35WB4RJUJ", "1P06CLSYGJWXIAJTCU0Q0WF2K9TIACQ2R8BG", "1P06CLSYGJWXIAJTCUQBVP154W5005SX94F1", "COMPANY"], "excludeContexts": [], "autoAssign": true, "defaultType": false, "color": "#FFF2F1", "new": false, "extContext": null, "extContextId": null, "extSystem": null}], "isScribbleEnabled": true}, "result": true, "code": null, "message": null, "additionalInfo": null, "requestId": "1f117812-a566-4c55-b239-366546635f57"}