#!/usr/bin/env python3
"""
Test the Fixed Mapping Implementation
Validates touchpoint and flow type mappings are working correctly
"""

import json
import sys
import os

# Add the directory to path to import our fixed migrator
sys.path.append('/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram')

def test_mappings():
    print("🧪 Testing Fixed Mapping Implementation")
    print("="*50)
    
    # Import our fixed migrator
    try:
        from fixed_mapping_migrator import FixedTotangoGainsightMigrator
    except ImportError as e:
        print(f"❌ Failed to import fixed migrator: {e}")
        return False
    
    # Initialize the migrator
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    migrator = FixedTotangoGainsightMigrator(config_file)
    
    # Test data - sample activities with touchpoint tags and flow types
    test_activities = [
        {
            "id": "test1",
            "properties": {
                "touchpoint_tags": ["12980f6d-797c-47da-ab58-78f3a3e5c954"],  # SCANNER MAINTENANCE
                "activity_type_id": "renewal",  # Should map to Renewal flow type
                "meeting_type": "test_meeting"
            },
            "note_content": {"text": "Test activity with touchpoint and flow mapping"}
        },
        {
            "id": "test2", 
            "properties": {
                "touchpoint_tags": ["9165fe90-5bc2-480d-98b2-76f3926c8e23", "38ce7c4e-374f-42db-94e8-23ba48d4b1e3"],  # SUPPORT, ESCALATION
                "activity_type_id": "support",  # Should map to Support flow type
                "meeting_type": "test_meeting"
            },
            "note_content": {"text": "Test activity with multiple touchpoints"}
        },
        {
            "id": "test3",
            "properties": {
                "touchpoint_tags": ["cefe339e-50d6-434d-a126-d66d652ce06c"],  # MEND AI
                "activity_type_id": "onboarding_101",  # Should map to Onboarding flow type
                "meeting_type": "test_meeting"
            },
            "note_content": {"text": "Test activity for AI and onboarding"}
        }
    ]
    
    print("🔍 Testing Touchpoint Reason Mapping:")
    for i, activity in enumerate(test_activities, 1):
        print(f"\n--- Test Activity {i} ---")
        result = migrator.map_touchpoint_reason(activity)
        
        touchpoint_tags = activity["properties"]["touchpoint_tags"]
        print(f"Input touchpoint tags: {touchpoint_tags}")
        
        if result:
            print(f"✅ Mapped to Gainsight ID: {result}")
            # Verify the mapping is correct
            for tag in touchpoint_tags:
                if tag in migrator.touchpoint_id_mapping:
                    expected = migrator.touchpoint_id_mapping[tag]
                    display_name = migrator.touchpoint_display_names.get(tag, tag)
                    print(f"   {tag} ({display_name}) -> {expected}")
                    if result == expected:
                        print(f"   ✅ Mapping verified!")
                    else:
                        print(f"   ❌ Mapping incorrect!")
                    break
        else:
            print(f"❌ No mapping found")
    
    print("\n🔍 Testing Flow Type Mapping:")
    for i, activity in enumerate(test_activities, 1):
        print(f"\n--- Test Activity {i} ---")
        result = migrator.map_flow_type(activity)
        
        activity_type_id = activity["properties"]["activity_type_id"]
        print(f"Input activity_type_id: {activity_type_id}")
        
        if result:
            print(f"✅ Mapped to Gainsight ID: {result}")
            # Verify the mapping is correct
            if activity_type_id in migrator.flow_type_id_mapping:
                expected = migrator.flow_type_id_mapping[activity_type_id]
                display_name = migrator.flow_type_display_names.get(activity_type_id, activity_type_id)
                print(f"   {activity_type_id} ({display_name}) -> {expected}")
                if result == expected:
                    print(f"   ✅ Mapping verified!")
                else:
                    print(f"   ❌ Mapping incorrect!")
        else:
            print(f"❌ No mapping found")
    
    # Test summary
    print("\n" + "="*50)
    print("📊 MAPPING VALIDATION SUMMARY:")
    print(f"✅ Touchpoint ID mappings available: {len(migrator.touchpoint_id_mapping)}")
    print(f"✅ Flow type ID mappings available: {len(migrator.flow_type_id_mapping)}")
    
    # Show some example mappings
    print("\n📋 Sample Touchpoint Mappings:")
    for i, (totango_id, gainsight_id) in enumerate(list(migrator.touchpoint_id_mapping.items())[:5]):
        display_name = migrator.touchpoint_display_names.get(totango_id, totango_id)
        print(f"   {totango_id[:8]}... ({display_name}) -> {gainsight_id}")
    
    print("\n📋 Sample Flow Type Mappings:")
    for i, (totango_id, gainsight_id) in enumerate(list(migrator.flow_type_id_mapping.items())[:5]):
        display_name = migrator.flow_type_display_names.get(totango_id, totango_id)
        print(f"   {totango_id} ({display_name}) -> {gainsight_id}")
    
    print("\n✅ Fixed mapping validation completed!")
    return True

if __name__ == "__main__":
    test_mappings()
