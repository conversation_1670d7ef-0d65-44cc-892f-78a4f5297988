#!/usr/bin/env python3
"""
Enhanced Totango to Gainsight Migrator - Complete Working Version with Touchpoint & Flow Type Mapping
Includes touchpoint reason and flow type mapping that your friend's code was missing
"""

import requests
import json
import time
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional
import re

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("TotangoMigrator")

class EnhancedTotangoGainsightMigrator:
    
    def __init__(self, config_file=None):
        self.config = self.load_config(config_file)
        self.session = requests.Session()
        self.results = {
            'success': 0,
            'failed': 0,
            'errors': [],
            'activity_types': {},
            'touchpoint_mappings': {},
            'flow_type_mappings': {}
        }
        self.cache = {
            'users': {},
            'activity_types': {},
            'touchpoint_types': {},
            'flow_types': {},
            'meeting_types': {},
            'gainsight_touchpoint_reasons': {},
            'gainsight_flow_types': {},
            'users_loaded': False,
            'touchpoint_mappings_loaded': False
        }
        
        # Enhanced mappings from your UI approach that your friend's code missed
        self.enhanced_mappings = {
            'touchpoint_reasons': {
                # Common touchpoint reasons from your CSV approach
                'check_in': 'Check-in',
                'issue_resolution': 'Issue Resolution',
                'product_demo': 'Product Demo',
                'training': 'Training',
                'implementation': 'Implementation',
                'renewal_discussion': 'Renewal Discussion',
                'expansion_opportunity': 'Expansion Opportunity',
                'onboarding': 'Onboarding',
                'support': 'Support',
                'escalation': 'Escalation',
                'follow_up': 'Follow Up',
                'quarterly_review': 'Quarterly Review'
            },
            'flow_types': {
                # Flow types from your comprehensive mapping
                'inbound': 'Inbound',
                'outbound': 'Outbound',
                'follow_up': 'Follow Up',
                'escalation': 'Escalation',
                'onboarding': 'Onboarding',
                'renewal': 'Renewal',
                'expansion': 'Expansion',
                'support': 'Support',
                'training': 'Training',
                'implementation': 'Implementation'
            }
        }
        
        # Direct mappings from your working code (Totango ID -> Gainsight ID)
        self.touchpoint_id_mapping = {
            "12980f6d-797c-47da-ab58-78f3a3e5c954": "1I00J1INQCQ6GQ3T2MID7L1S4X8HPJ2OP95B",  # SCANNER MAINTENANCE
            "16a15fcd-d735-49a7-8b14-ca279e6dba5a": "1I00J1INQCQ6GQ3T2MOPQWUV1NFURNPEK8VE",  # MEND CONTAINER
            "30989b43-ff62-486c-b4db-f89c3106abba": "1I00J1INQCQ6GQ3T2MWULITYE9ASDFPQ2KD3",  # ACCOUNT REVIEW
            "38bb62d0-4b50-4a5f-906c-6edbfe4786db": "1I00J1INQCQ6GQ3T2MYNG8BC6SJCCN7SAPKC",  # MEND SCA
            "38ce7c4e-374f-42db-94e8-23ba48d4b1e3": "1I00J1INQCQ6GQ3T2MTA6WH5AKWS5JL0O87R",  # ESCALATION
            "6cc323e4-4155-48bf-964c-ec75ee70e96b": "1I00J1INQCQ6GQ3T2MGRUSTP2XZGZBXN5263",  # MEND PLATFORM ACCESS
            "7c285778-76be-4543-b8a1-9066ee5a1ad4": "1I00J1INQCQ6GQ3T2M0O5F16H63MEMNDN97J",  # MEND SAST
            "9165fe90-5bc2-480d-98b2-76f3926c8e23": "1I00J1INQCQ6GQ3T2MY27A9SQHEE253247F2",  # SUPPORT
            "a2d4c216-8d7b-4f24-9cdf-3c4bc0226f94": "1I00J1INQCQ6GQ3T2MJY0GQI8EZ9STP11VR2",  # MARKETING
            "b1120b9a-85e9-4c2c-b7e7-9214ac3baaea": "1I00J1INQCQ6GQ3T2M3D94LFFY2R8UMNJXE0",  # NPS
            "c5d4cd04-1224-4ad3-8696-d2fb70ea85be": "1I00J1INQCQ6GQ3T2MXT1PJXBF3KATW6QR4K",  # PRODUCT MANAGEMENT ENGAGEMENT
            "cbd1f1f4-229a-4e1b-b4a3-a091e90932cc": "1I00J1INQCQ6GQ3T2M6MLMI12JOYH1Z05ILP",  # ONBOARDING
            "ceb3f746-7099-4083-bc18-7d941699c6b5": "1I00J1INQCQ6GQ3T2M9VQOCBJ5RVU2YW5V4B",  # REACHABILITY (SCA)
            "cefe339e-50d6-434d-a126-d66d652ce06c": "1I00J1INQCQ6GQ3T2MENNOYNKWB0ZEM5WII6",  # MEND AI
            "ddf60bee-a1d2-4b03-b1bd-1f59eef0e214": "1I00J1INQCQ6GQ3T2M2M84A559MT05AWXZT7",  # OUTAGE
            "e43ae486-e0b8-4e2c-a8f3-fe17821b003d": "1I00J1INQCQ6GQ3T2MRYA87NSXMM46FZCUZH",  # CADENCE
            "e4b6a6cf-0833-4a60-aac2-52759e73e9ab": "1I00J1INQCQ6GQ3T2M16QWTIDVSRJTOA8219",  # RENEWAL
            "eb5d8037-0f63-452e-8007-977e91e061b5": "1I00J1INQCQ6GQ3T2MZKWMBM3ZATASUU2VFS",  # REFERENCE
            "ed358b0e-5cad-42e6-80be-04df0db7dc2c": "1I00J1INQCQ6GQ3T2MQN0KLL5SPTQZYCWWG6",  # FEE
            "f9f8cc62-1436-4f96-afe7-ef77a2ff18ed": "1I00J1INQCQ6GQ3T2MNUINIM89ZGC48DOXQR"   # SAST CLOUD MIGRATION
        }
        
        # Direct flow type mappings (Totango activity_type_id -> Gainsight Flow Type ID)
        self.flow_type_id_mapping = {
            "renewal": "1I00EBMOY66ROGCBY6844WMEDZF8B8RXHTP1",                    # Renewal
            "support": "1I00EBMOY66ROGCBY66HJSD9I0AG8E8Q1A0D",                    # Support
            "upsell": "1I00EBMOY66ROGCBY6J9O3U5YYU9F5NDTN2X",                     # Upsell
            "escalation": "1I00EBMOY66ROGCBY6L0K6AGIJF5FPS2BATD",                # Escalation
            "onboarding_101": "1I00EBMOY66ROGCBY63I51PAXG5OJRPK37AQ",            # Onboarding
            "adoption": "1I00EBMOY66ROGCBY6IJJ2GVGY81F62M23Z6",                   # Adoption
            "risk_1560979263618": "1I00EBMOY66ROGCBY6YKHPSNQCJ382C3YQQU",        # Risk
            "product_transition_1630456786595": "1I00EBMOY66ROGCBY6F8BZABGHFA7Z1OOYW5",  # Product Transition
            "intelligence_1561140678082": "1I00EBMOY66ROGCBY6H0LHB0SGQLAU3N58O4",     # Intelligence
            "services_1631204797082": "1I00EBMOY66ROGCBY650ON17KW7GYGD2H5ZY",        # Services
            "inbound_1631240727463": "1I00EBMOY66ROGCBY6YAF5US4GK95QEO5ZFU",         # Inbound
            "design_partner_1635451768576": "1I00EBMOY66ROGCBY6F8NQVJY43GPGH1XTD8",   # Design Partner
            "nps_1654203738879": "1I00EBMOY66ROGCBY68KLFLZ3KPM5GGP9YUH",            # NPS
            "business_review_1628207371592": "1I00EBMOY66ROGCBY6EOK9JO7C3HUUM9O5F7",  # Business Review
            "journey_1713548309375": "1I00EBMOY66ROGCBY6QL90GOMJA5GILCWWLT",         # Journey
            "lifecycle_1714094340032": "1I00EBMOY66ROGCBY63E4BLUKMID71508EXR"        # Lifecycle
        }
        
    def load_config(self, config_file):
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r') as f:
                return json.load(f)
        return {}
    
    def get_totango_activities(self, account_ids: List[str]) -> List[Dict]:
        logger.info(f"Extracting activities with meeting_type from {len(account_ids)} Totango accounts...")
        
        all_activities = []
        
        for account_id in account_ids:
            try:
                url = f"{self.config['totango']['url']}/t01/mend/api/v2/events/"
                params = {
                    'account_id': account_id,
                    'include_formatting': 'true'
                }
                
                response = self.session.get(
                    url,
                    headers=self.config['totango']['headers'],
                    params=params,
                    timeout=30
                )
                
                if response.status_code == 200:
                    events = response.json()
                    
                    # Filter for events with meeting_type property ONLY (like your converter)
                    meeting_events = []
                    for event in events:
                        properties = event.get('properties', {})
                        if 'meeting_type' in properties and properties['meeting_type']:
                            event['sourceAccountId'] = account_id
                            meeting_events.append(event)
                    
                    all_activities.extend(meeting_events)
                    logger.info(f"Account {account_id}: {len(meeting_events)} activities with meeting_type (out of {len(events)} total)")
                    
                else:
                    logger.error(f"Failed for account {account_id}: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"Error with account {account_id}: {e}")
                
        logger.info(f"Total activities with meeting_type extracted: {len(all_activities)} (should be 37)")
        return all_activities
    
    def is_relevant_event(self, event: Dict) -> bool:
        properties = event.get('properties', {})
        
        if 'meeting_type' in properties:
            return True
            
        if 'note_content' in event and event['note_content']:
            return True
            
        meaningful_fields = ['subject', 'description', 'content', 'message']
        for field in meaningful_fields:
            if field in properties and properties[field]:
                return True
                
        return False
    
    def get_gainsight_users(self) -> Dict[str, Dict]:
        if self.cache['users_loaded']:
            return self.cache['users']
            
        logger.info("Loading Gainsight users...")
        
        try:
            all_users = []
            page_number = 1
            
            while True:
                payload = {
                    'limit': 50,
                    'pageNumber': page_number,
                    'searchString': '',
                    'clause': None,
                    'fields': ['Email', 'Gsid', 'Name']
                }
                
                response = self.session.post(
                    f"{self.config['gainsight']['url']}/v1/dataops/gdm/list?object=GsUser",
                    json=payload,
                    headers=self.config['gainsight']['headers'],
                    timeout=30
                )
                
                if response.status_code != 200:
                    break
                    
                users = response.json().get('data', {}).get('data', [])
                if not users:
                    break
                    
                all_users.extend(users)
                page_number += 1
                
            for user in all_users:
                email = user.get('Email', '').lower()
                if email:
                    self.cache['users'][email] = {
                        'id': user.get('Gsid', ''),
                        'name': user.get('Name', ''),
                        'email': user.get('Email', '')
                    }
                    
            self.cache['users_loaded'] = True
            logger.info(f"Loaded {len(self.cache['users'])} users")
            
        except Exception as e:
            logger.error(f"Failed to load users: {e}")
            
        return self.cache['users']
    
    def get_gainsight_activity_types(self, company_id: str) -> Dict[str, str]:
        logger.info("Loading activity types...")
        
        try:
            url = f"{self.config['gainsight']['url']}/v1/ant/forms"
            params = {
                'context': 'Company',
                'contextId': company_id,
                'showHidden': 'false'
            }
            
            response = self.session.get(
                url,
                params=params,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )
            
            logger.info(f"Activity types API status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data and 'activityTypes' in data['data']:
                    activity_types = data['data']['activityTypes']
                    
                    type_mapping = {}
                    for activity_type in activity_types:
                        name = activity_type.get('name', '').upper()
                        type_id = activity_type.get('id', '')
                        if name and type_id:
                            type_mapping[name] = type_id
                    
                    logger.info(f"Successfully loaded {len(type_mapping)} activity types")
                    
                    # Log the types for debugging
                    for name, type_id in type_mapping.items():
                        logger.info(f"  {name}: {type_id}")
                    
                    # Store in cache and results
                    self.cache['activity_types'] = type_mapping
                    self.results['activity_types'] = type_mapping
                    
                    return type_mapping
                else:
                    logger.error(f"Unexpected response structure. Keys: {list(data.keys())}")
                    if 'data' in data:
                        logger.error(f"Data keys: {list(data['data'].keys())}")
            else:
                logger.error(f"API call failed: {response.status_code} - {response.text[:200]}")
                
        except Exception as e:
            logger.error(f"Exception loading activity types: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            
        return {}
    
    def get_totango_touchpoint_types(self) -> Dict[str, str]:
        """Fetch touchpoint types from Totango API"""
        logger.info("Loading Totango touchpoint types...")
        
        try:
            url = f"{self.config['totango']['url']}/t01/mend/api/v3/touchpoint-types/"
            
            response = self.session.get(
                url,
                headers=self.config['totango']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                touchpoint_types = response.json()
                
                type_mapping = {}
                if isinstance(touchpoint_types, list):
                    for tp_type in touchpoint_types:
                        type_id = tp_type.get('id', '')
                        display_name = tp_type.get('display_name', '')
                        if type_id and display_name:
                            type_mapping[type_id] = display_name
                elif isinstance(touchpoint_types, dict) and 'data' in touchpoint_types:
                    for tp_type in touchpoint_types['data']:
                        type_id = tp_type.get('id', '')
                        display_name = tp_type.get('display_name', '')
                        if type_id and display_name:
                            type_mapping[type_id] = display_name
                
                logger.info(f"Loaded {len(type_mapping)} Totango touchpoint types")
                self.cache['touchpoint_types'] = type_mapping
                return type_mapping
            else:
                logger.warning(f"Failed to load touchpoint types: {response.status_code} - {response.text[:200]}")
                # Fallback to enhanced mappings
                logger.info("Using fallback touchpoint mappings")
                return self.enhanced_mappings['touchpoint_reasons']
                
        except Exception as e:
            logger.error(f"Error loading touchpoint types: {e}")
            # Fallback to enhanced mappings
            logger.info("Using fallback touchpoint mappings due to API error")
            return self.enhanced_mappings['touchpoint_reasons']
            
        return self.enhanced_mappings['touchpoint_reasons']
    
    def get_totango_flow_types(self) -> Dict[str, str]:
        """Fetch flow types from Totango API"""
        logger.info("Loading Totango flow types...")
        
        try:
            url = f"{self.config['totango']['url']}/t01/mend/api/v3/touchpoint-tags/"
            
            response = self.session.get(
                url,
                headers=self.config['totango']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                flow_types = response.json()
                
                type_mapping = {}
                if isinstance(flow_types, list):
                    for flow_type in flow_types:
                        activity_type_id = flow_type.get('activity_type_id', '')
                        display_name = flow_type.get('display_name', '')
                        if activity_type_id and display_name:
                            # Clean the display name like your converter does
                            clean_name = self.clean_flow_type_name(display_name)
                            type_mapping[activity_type_id] = clean_name
                elif isinstance(flow_types, dict) and 'data' in flow_types:
                    for flow_type in flow_types['data']:
                        activity_type_id = flow_type.get('activity_type_id', '')
                        display_name = flow_type.get('display_name', '')
                        if activity_type_id and display_name:
                            clean_name = self.clean_flow_type_name(display_name)
                            type_mapping[activity_type_id] = clean_name
                
                logger.info(f"Loaded {len(type_mapping)} Totango flow types")
                self.cache['flow_types'] = type_mapping
                return type_mapping
            else:
                logger.warning(f"Failed to load flow types: {response.status_code} - {response.text[:200]}")
                # Fallback to enhanced mappings
                logger.info("Using fallback flow type mappings")
                return self.enhanced_mappings['flow_types']
                
        except Exception as e:
            logger.error(f"Error loading flow types: {e}")
            # Fallback to enhanced mappings
            logger.info("Using fallback flow type mappings due to API error")
            return self.enhanced_mappings['flow_types']
            
        return self.enhanced_mappings['flow_types']
    
    def clean_flow_type_name(self, flow_type_name: str) -> str:
        """Clean flow type name by removing timestamps and numbers (like your converter)."""
        if not flow_type_name:
            return ""

        # Remove timestamp patterns like _1560979263618
        import re
        cleaned = re.sub(r'_\d{10,}', '', flow_type_name)

        # Remove trailing numbers like _1, _2, etc.
        cleaned = re.sub(r'_\d+$', '', cleaned)

        # Replace underscores with spaces and title case
        cleaned = cleaned.replace('_', ' ').title()

        return cleaned
    
    def load_mapping_files(self):
        """Load mapping files directly (like your converter does)"""
        logger.info("Loading mapping files from JSON files...")
        
        base_path = "/Users/<USER>/Desktop/totango"
        
        # Load ID mappings (meeting types) - like your ID.json
        try:
            id_file = f"{base_path}/ID.json"
            with open(id_file, 'r', encoding='utf-8') as f:
                id_data = json.load(f)
            self.cache['meeting_types'] = {item['id']: item['display_name'] for item in id_data}
            logger.info(f"Loaded {len(self.cache['meeting_types'])} ID mappings from {id_file}")
        except Exception as e:
            logger.error(f"Error loading ID mappings: {e}")
            
        # Load touchpoint reason mappings - like your Touchpoint_reason.JSON
        try:
            touchpoint_file = f"{base_path}/Touchpoint_reason.JSON"
            with open(touchpoint_file, 'r', encoding='utf-8') as f:
                touchpoint_data = json.load(f)
            self.cache['touchpoint_types'] = {item['id']: item['display_name'] for item in touchpoint_data}
            logger.info(f"Loaded {len(self.cache['touchpoint_types'])} touchpoint mappings from {touchpoint_file}")
        except Exception as e:
            logger.error(f"Error loading touchpoint mappings: {e}")
            
        # Load flow type mappings - like your flowtype.json
        try:
            flow_file = f"{base_path}/flowtype.json"
            with open(flow_file, 'r', encoding='utf-8') as f:
                flow_data = json.load(f)
            # Use activity_type_id field (correct field for flow types)
            for item in flow_data:
                flow_id = item.get('activity_type_id', '')
                display_name = item.get('display_name', '')
                if flow_id and display_name:
                    clean_name = self.clean_flow_type_name(display_name)
                    self.cache['flow_types'][flow_id] = clean_name
            logger.info(f"Loaded {len(self.cache['flow_types'])} flow type mappings from {flow_file}")
        except Exception as e:
            logger.error(f"Error loading flow type mappings: {e}")
    
    def get_gainsight_touchpoint_reasons(self) -> Dict[str, str]:
        """Fetch touchpoint reasons from Gainsight API"""
        logger.info("Loading Gainsight touchpoint reasons...")
        
        try:
            url = f"{self.config['gainsight']['url']}/v1/ant/picklist/items/category/"
            params = {
                'ct': '',
                'id': '1I00IURNPETEW0S7CAODIK73WMX9JA9GJVMQ',
                'ref': ''
            }
            
            response = self.session.get(
                url,
                params=params,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                reason_mapping = {}
                if 'data' in data:
                    for item in data['data']:
                        name = item.get('name', '').lower()
                        value = item.get('value', '')
                        if name and value:
                            reason_mapping[name] = value
                
                logger.info(f"Loaded {len(reason_mapping)} Gainsight touchpoint reasons")
                self.cache['gainsight_touchpoint_reasons'] = reason_mapping
                return reason_mapping
            else:
                logger.warning(f"Failed to load touchpoint reasons: {response.status_code} - {response.text[:200]}")
                # Fallback to enhanced mappings - convert keys to values
                fallback_mapping = {v.lower(): v for v in self.enhanced_mappings['touchpoint_reasons'].values()}
                logger.info(f"Using fallback touchpoint reasons: {len(fallback_mapping)}")
                return fallback_mapping
                
        except Exception as e:
            logger.error(f"Error loading touchpoint reasons: {e}")
            # Fallback to enhanced mappings
            fallback_mapping = {v.lower(): v for v in self.enhanced_mappings['touchpoint_reasons'].values()}
            logger.info(f"Using fallback touchpoint reasons due to API error: {len(fallback_mapping)}")
            return fallback_mapping
            
        # Final fallback
        fallback_mapping = {v.lower(): v for v in self.enhanced_mappings['touchpoint_reasons'].values()}
        return fallback_mapping
    
    def get_gainsight_flow_types(self) -> Dict[str, str]:
        """Fetch flow types from Gainsight API"""
        logger.info("Loading Gainsight flow types...")
        
        try:
            # Using the same API endpoint as touchpoint reasons since they share the same structure
            url = f"{self.config['gainsight']['url']}/v1/ant/picklist/items/category/"
            params = {
                'ct': '',
                'id': '1I00IURNPETEW0S7CAODIK73WMX9JA9GJVMQ',
                'ref': ''
            }
            
            response = self.session.get(
                url,
                params=params,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                flow_mapping = {}
                if 'data' in data:
                    for item in data['data']:
                        name = item.get('name', '').lower()
                        value = item.get('value', '')
                        if name and value:
                            flow_mapping[name] = value
                
                logger.info(f"Loaded {len(flow_mapping)} Gainsight flow types")
                self.cache['gainsight_flow_types'] = flow_mapping
                return flow_mapping
            else:
                logger.warning(f"Failed to load flow types: {response.status_code} - {response.text[:200]}")
                # Fallback to enhanced mappings - convert keys to values
                fallback_mapping = {v.lower(): v for v in self.enhanced_mappings['flow_types'].values()}
                logger.info(f"Using fallback flow types: {len(fallback_mapping)}")
                return fallback_mapping
                
        except Exception as e:
            logger.error(f"Error loading flow types: {e}")
            # Fallback to enhanced mappings
            fallback_mapping = {v.lower(): v for v in self.enhanced_mappings['flow_types'].values()}
            logger.info(f"Using fallback flow types due to API error: {len(fallback_mapping)}")
            return fallback_mapping
            
        # Final fallback
        fallback_mapping = {v.lower(): v for v in self.enhanced_mappings['flow_types'].values()}
        return fallback_mapping
    
    def map_touchpoint_reason(self, totango_activity: Dict, gainsight_reasons: Dict) -> Optional[str]:
        """Map Totango touchpoint data to Gainsight touchpoint reason (like your converter)"""
        properties = totango_activity.get('properties', {})
        
        # Check for touchpoint_tags in the activity (this is the field name in Totango API)
        touchpoint_tags = properties.get('touchpoint_tags')
        if touchpoint_tags:
            # Handle different formats (string or list)
            if isinstance(touchpoint_tags, str):
                tag_ids = [touchpoint_tags]
            elif isinstance(touchpoint_tags, list):
                tag_ids = touchpoint_tags
            else:
                tag_ids = []
            
            # Map the IDs to display names using loaded touchpoint mapping
            mapped_names = []
            for tag_id in tag_ids:
                if tag_id in self.cache.get('touchpoint_types', {}):
                    display_name = self.cache['touchpoint_types'][tag_id]
                    mapped_names.append(display_name)
                    logger.info(f"Mapped touchpoint tag {tag_id} -> {display_name}")
            
            if mapped_names:
                return "; ".join(mapped_names)
            else:
                # Had touchpoint_tags but couldn't map them
                return "Internal Note"
        
        # If no touchpoint_tags, return None (like your converter logic)
        return None
    
    def map_flow_type(self, totango_activity: Dict, gainsight_flow_types: Dict) -> Optional[str]:
        """Map Totango flow data to Gainsight flow type (like your converter)"""
        properties = totango_activity.get('properties', {})
        
        # Check for activity_type_id field (this is the correct field from your converter)
        activity_type_id = properties.get('activity_type_id')
        if activity_type_id and activity_type_id in self.cache.get('flow_types', {}):
            flow_type_name = self.cache['flow_types'][activity_type_id]
            logger.info(f"Mapped flow type {activity_type_id} -> {flow_type_name}")
            return flow_type_name
        
        # Check for other possible flow type fields
        for field_name in ['flow_type', 'flow_type_id', 'type_id']:
            if field_name in properties:
                flow_type_id = properties[field_name]
                if flow_type_id in self.cache.get('flow_types', {}):
                    flow_type_name = self.cache['flow_types'][flow_type_id]
                    logger.info(f"Mapped flow type {flow_type_id} -> {flow_type_name}")
                    return flow_type_name
        
        # Default fallback (like your converter)
        return "Standard"
    
    def get_activity_type_name_from_id(self, activity_type_id: str, activity_types: Dict[str, str]) -> str:
        """Get activity type name from ID for the 'type' field in note"""
        for name, type_id in activity_types.items():
            if type_id == activity_type_id:
                return name
        return "Activity"
    
    def map_activity_type(self, meeting_type_id: str, gainsight_types: Dict[str, str]) -> str:
        """Map Totango meeting type ID to Gainsight activity type ID (like your friend's code)"""
        if not meeting_type_id:
            return gainsight_types.get('EMAIL', list(gainsight_types.values())[0] if gainsight_types else '')
        
        # First, get the display name from the meeting type ID
        meeting_type_display_name = ""
        if meeting_type_id in self.cache.get('meeting_types', {}):
            meeting_type_display_name = self.cache['meeting_types'][meeting_type_id]
        elif meeting_type_id in self.cache.get('touchpoint_types', {}):
            meeting_type_display_name = self.cache['touchpoint_types'][meeting_type_id]
        
        if not meeting_type_display_name:
            meeting_type_display_name = meeting_type_id
        
        logger.info(f"Mapping meeting type ID {meeting_type_id} -> display name: {meeting_type_display_name}")
        
        # Now map the display name to Gainsight activity type using your converter logic
        mappings = {
            # Direct mappings (case-sensitive)
            "Email": "EMAIL",
            "Telephone Call": "CALL", 
            "Web Meeting": "MEETING",
            "Internal Note": "UPDATE",
            "Meeting": "MEETING",
            "Update": "UPDATE",
            
            # Case-insensitive variations
            "email": "EMAIL",
            "Email Campaign": "EMAIL",
            "telephone call": "CALL",
            "Phone Call": "CALL",
            "web meeting": "MEETING",
            "Video Meeting": "MEETING", 
            "internal note": "UPDATE",
            "Note": "UPDATE"
        }
        
        # Direct mapping first
        if meeting_type_display_name in mappings:
            gainsight_type = mappings[meeting_type_display_name]
            if gainsight_type in gainsight_types:
                logger.info(f"Direct mapping: {meeting_type_display_name} -> {gainsight_type}")
                return gainsight_types[gainsight_type]
        
        # Case-insensitive mapping
        for totango_type, gainsight_type in mappings.items():
            if meeting_type_display_name.lower() == totango_type.lower():
                if gainsight_type in gainsight_types:
                    logger.info(f"Case-insensitive mapping: {meeting_type_display_name} -> {gainsight_type}")
                    return gainsight_types[gainsight_type]
        
        # Pattern matching (like your converter)
        clean_type = meeting_type_display_name.lower()
        if "email" in clean_type or "mail" in clean_type:
            if 'EMAIL' in gainsight_types:
                logger.info(f"Pattern matching: {meeting_type_display_name} -> EMAIL")
                return gainsight_types['EMAIL']
        elif "call" in clean_type or "phone" in clean_type or "telephone" in clean_type:
            if 'CALL' in gainsight_types:
                logger.info(f"Pattern matching: {meeting_type_display_name} -> CALL")
                return gainsight_types['CALL']
        elif "meeting" in clean_type or "conference" in clean_type or "video" in clean_type:
            if 'MEETING' in gainsight_types:
                logger.info(f"Pattern matching: {meeting_type_display_name} -> MEETING")
                return gainsight_types['MEETING']
        elif "slack" in clean_type or "chat" in clean_type:
            if 'SLACK' in gainsight_types:
                return gainsight_types['SLACK']
        elif "feedback" in clean_type or "survey" in clean_type:
            if 'FEEDBACK' in gainsight_types:
                return gainsight_types['FEEDBACK']
        elif "inbound" in clean_type or "support" in clean_type:
            if 'INBOUND' in gainsight_types:
                return gainsight_types['INBOUND']
        elif "gong" in clean_type:
            if 'GONG CALL' in gainsight_types:
                return gainsight_types['GONG CALL']
        elif "person" in clean_type and "meeting" in clean_type:
            if 'IN PERSON MEETING' in gainsight_types:
                return gainsight_types['IN PERSON MEETING']
            
        # Default fallback
        if 'UPDATE' in gainsight_types:
            logger.info(f"Default fallback: {meeting_type_display_name} -> UPDATE")
            return gainsight_types['UPDATE']
            
        return list(gainsight_types.values())[0] if gainsight_types else ''
    
    def transform_activity(self, activity: Dict, users: Dict, activity_types: Dict, 
                          touchpoint_reasons: Dict = None, flow_types: Dict = None) -> Optional[Dict]:
        try:
            properties = activity.get('properties', {})
            
            subject = self.extract_subject(activity)
            content = self.extract_content(activity)
            meeting_type_id = properties.get('meeting_type', '')
            timestamp = activity.get('timestamp', '')
            
            # Map meeting type ID to Gainsight activity type ID (like your friend's code)
            activity_type_id = self.map_activity_type(meeting_type_id, activity_types)
            if not activity_type_id:
                return None
                
            activity_date = self.format_timestamp(timestamp)
            author_info = self.extract_author_info(activity, users)
            
            # Map touchpoint reason and flow type (enhancement over your friend's approach)
            touchpoint_reason = None
            flow_type = None
            
            if touchpoint_reasons:
                touchpoint_reason = self.map_touchpoint_reason(activity, touchpoint_reasons)
            
            if flow_types:
                flow_type = self.map_flow_type(activity, flow_types)
            
            # Enhanced custom fields with touchpoint reason and flow type (matching Gainsight payload format)
            custom_fields = {
                'internalAttendees': [
                    {
                        'id': author_info['id'],
                        'obj': 'User',
                        'name': author_info['name'],
                        'email': author_info['email'],
                        'eid': None,
                        'eobj': 'User',
                        'epp': None,
                        'esys': 'SALESFORCE',
                        'sys': 'GAINSIGHT',
                        'pp': ''
                    }
                ],
                'externalAttendees': [],
                'ant__Status1552512571338': None,
                'Ant__Touchpoint_Reason__c': None,
                'Ant__Flow_Type__c': None
            }
            
            # Add touchpoint reason if mapped (using correct field name from payload)
            if touchpoint_reason:
                custom_fields['Ant__Touchpoint_Reason__c'] = touchpoint_reason
                logger.info(f"Mapped touchpoint reason: {touchpoint_reason}")
            
            # Add flow type if mapped (using correct field name from payload)
            if flow_type:
                custom_fields['Ant__Flow_Type__c'] = flow_type
                logger.info(f"Mapped flow type: {flow_type}")
            
            payload = {
                'lastModifiedByUser': {
                    'gsId': author_info['id'],
                    'name': author_info['name'],
                    'eid': None,
                    'esys': None,
                    'pp': ''
                },
                'note': {
                    'customFields': custom_fields,
                    'type': self.get_activity_type_name_from_id(activity_type_id, activity_types),
                    'subject': subject,
                    'activityDate': activity_date,
                    'content': content,
                    'plainText': self.strip_html(content),
                    'trackers': None
                },
                'mentions': [],
                'relatedRecords': {},
                'meta': {
                    'activityTypeId': activity_type_id,
                    'ctaId': None,
                    'source': 'C360',
                    'hasTask': False,
                    'emailSent': False,
                    'systemType': 'GAINSIGHT',
                    'notesTemplateId': None
                },
                'author': {
                    'id': author_info['id'],
                    'obj': 'User',
                    'name': author_info['name'],
                    'email': author_info['email'],
                    'eid': None,
                    'eobj': 'User',
                    'epp': None,
                    'esys': 'SALESFORCE',
                    'sys': 'GAINSIGHT',
                    'pp': ''
                },
                'syncedToSFDC': False,
                'tasks': [],
                'attachments': [],
                'contexts': [
                    {
                        'id': self.config['gainsight']['company_id'],
                        'base': True,
                        'obj': 'Company',
                        'lbl': self.config['gainsight']['company_name'],
                        'eid': None,
                        'eobj': 'Account',
                        'eurl': None,
                        'esys': 'SALESFORCE',
                        'dsp': True
                    }
                ]
            }
            
            return payload
            
        except Exception as e:
            logger.error(f"Transform error: {e}")
            return None
    
    def extract_subject(self, activity: Dict) -> str:
        properties = activity.get('properties', {})
        
        for field in ['subject', 'name', 'display_name', 'title']:
            if field in properties and properties[field]:
                return str(properties[field])[:200]
                
        activity_type = activity.get('type', 'Activity')
        return f"Totango: {activity_type.replace('_', ' ').title()}"
    
    def extract_content(self, activity: Dict) -> str:
        """Generate HTML content for activity (like your converter)"""
        # Check for note_content.text at TOP LEVEL first (most important - like your converter)
        if 'note_content' in activity:
            note_content = activity['note_content']
            if isinstance(note_content, dict) and 'text' in note_content:
                text_content = note_content['text']
                if text_content and text_content.strip():
                    # Return the text as-is if it's already HTML, otherwise wrap in <p>
                    if text_content.startswith('<') and ('</p>' in text_content or '</div>' in text_content):
                        return text_content
                    else:
                        return f"<p>{text_content}</p>"

        # Fallback to properties if no note_content
        properties = activity.get('properties', {})
        for field in ['description', 'content', 'message', 'body', 'subject']:
            if field in properties and properties[field]:
                content = str(properties[field])
                if content.strip():
                    return f"<p>{content}</p>" if not content.startswith('<') else content
                    
        # Generate based on activity type if no content fields found
        activity_type = activity.get('type', 'activity')
        meeting_type_id = properties.get('meeting_type', '')
        meeting_type_name = self.cache.get('meeting_types', {}).get(meeting_type_id, 'Activity')
        
        return f"<p>Totango {meeting_type_name}: {activity_type.replace('_', ' ')}</p>"
    
    def extract_author_info(self, activity: Dict, users: Dict) -> Dict:
        enriched_users = activity.get('enrichedUsers', [])
        if enriched_users and isinstance(enriched_users, list):
            user = enriched_users[0]
            if isinstance(user, dict):
                email = user.get('email', '').lower()
                if email and email in users:
                    return users[email]
                    
        author = activity.get('author', {})
        if isinstance(author, dict):
            email = author.get('email', '').lower()
            if email and email in users:
                return users[email]
                
        return {
            'id': self.config['gainsight']['user_id'],
            'name': self.config['gainsight']['user_name'],
            'email': self.config['gainsight']['user_email']
        }
    
    def format_timestamp(self, timestamp) -> str:
        try:
            if isinstance(timestamp, str):
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            else:
                if timestamp > 10**10:
                    timestamp = timestamp / 1000
                dt = datetime.fromtimestamp(timestamp)
                
            return dt.isoformat()
        except:
            return datetime.now().isoformat()
    
    def strip_html(self, html: str) -> str:
        if not html:
            return ""
        return re.sub(r'<[^>]*>', '', html)
    
    def create_draft(self, payload: Dict) -> Optional[str]:
        try:
            url = f"{self.config['gainsight']['url']}/v1/ant/v2/activity/drafts"
            
            response = self.session.post(
                url,
                json=payload,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                draft_data = response.json()
                draft_id = draft_data.get('data', {}).get('id')
                return draft_id
            else:
                logger.error(f"Draft failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Draft error: {e}")
            return None
    
    def create_timeline_entry(self, payload: Dict, draft_id: str) -> bool:
        try:
            url = f"{self.config['gainsight']['url']}/v1/ant/v2/activity"
            
            timeline_payload = payload.copy()
            timeline_payload['id'] = draft_id
            
            response = self.session.post(
                url,
                json=timeline_payload,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                return True
            else:
                logger.error(f"Timeline failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Timeline error: {e}")
            return False
    
    def migrate_single_activity(self, activity: Dict, users: Dict, activity_types: Dict, 
                               touchpoint_reasons: Dict = None, flow_types: Dict = None) -> Dict:
        result = {
            'success': False,
            'activity_id': activity.get('id', 'unknown'),
            'account_id': activity.get('sourceAccountId', 'unknown'),
            'error': None
        }
        
        try:
            payload = self.transform_activity(activity, users, activity_types, touchpoint_reasons, flow_types)
            if not payload:
                result['error'] = 'Transform failed'
                return result
                
            draft_id = self.create_draft(payload)
            if not draft_id:
                result['error'] = 'Draft failed'
                return result
                
            if self.create_timeline_entry(payload, draft_id):
                result['success'] = True
                logger.info(f"Migrated activity for {result['account_id']}")
            else:
                result['error'] = 'Timeline failed'
                
        except Exception as e:
            result['error'] = str(e)
            
        return result
    
    def migrate_all_activities(self, account_ids: List[str]):
        logger.info("Starting enhanced migration...")
        
        start_time = time.time()
        
        activities = self.get_totango_activities(account_ids)
        if not activities:
            logger.error("No activities found")
            return
            
        logger.info("Loading Gainsight data...")
        users = self.get_gainsight_users()
        
        # Get the company_id from config
        company_id = self.config['gainsight']['company_id']
        logger.info(f"Using company ID: {company_id}")
        
        activity_types = self.get_gainsight_activity_types(company_id)
        
        if not activity_types:
            logger.error("Failed to load activity types - aborting migration")
            return
        
        # Load touchpoint and flow type mappings from JSON files (like your converter)
        logger.info("Loading mapping files...")
        self.load_mapping_files()
        
        # These are now loaded from files, not APIs
        gainsight_touchpoint_reasons = {}  # Not needed since we use file mappings
        gainsight_flow_types = {}  # Not needed since we use file mappings
        
        # Store mappings in results for reporting
        self.results['touchpoint_mappings'] = {
            'totango_meeting_types': self.cache.get('meeting_types', {}),
            'totango_touchpoint_types': self.cache.get('touchpoint_types', {}),
            'gainsight_reasons': gainsight_touchpoint_reasons
        }
        self.results['flow_type_mappings'] = {
            'totango_flow_types': self.cache.get('flow_types', {}),
            'gainsight_types': gainsight_flow_types
        }
        
        logger.info(f"Mapping summary:")
        logger.info(f"  - Totango meeting types: {len(self.cache.get('meeting_types', {}))}")
        logger.info(f"  - Totango touchpoint types: {len(self.cache.get('touchpoint_types', {}))}")
        logger.info(f"  - Gainsight touchpoint reasons: {len(gainsight_touchpoint_reasons)}")
        logger.info(f"  - Totango flow types: {len(self.cache.get('flow_types', {}))}")
        logger.info(f"  - Gainsight flow types: {len(gainsight_flow_types)}")
            
        logger.info(f"Processing {len(activities)} activities...")
        
        for activity in activities:
            result = self.migrate_single_activity(
                activity, users, activity_types, 
                gainsight_touchpoint_reasons, gainsight_flow_types
            )
            
            if result['success']:
                self.results['success'] += 1
            else:
                self.results['failed'] += 1
                self.results['errors'].append(result)
            
            time.sleep(1.0)  # Rate limiting
            
        end_time = time.time()
        self.generate_report(end_time - start_time)
        self.save_results()
    
    def generate_report(self, total_time: float):
        total = self.results['success'] + self.results['failed']
        success_rate = (self.results['success'] / total * 100) if total > 0 else 0
        
        report = f"""
🚀 ENHANCED TOTANGO TO GAINSIGHT MIGRATION REPORT
{'='*60}
📊 STATISTICS:
    Total Activities: {total:,}
    ✅ Successful: {self.results['success']:,}
    ❌ Failed: {self.results['failed']:,}
    📈 Success Rate: {success_rate:.1f}%
    ⏱️ Total Time: {total_time:.1f} seconds

🎯 GAINSIGHT ACTIVITY TYPES:
"""
        
        for type_name, type_id in self.results['activity_types'].items():
            report += f"    {type_name}: {type_id}\n"
        
        # Add touchpoint and flow type mapping report
        if self.results.get('touchpoint_mappings'):
            report += f"""
🎯 TOUCHPOINT MAPPING SUMMARY:
    Totango Meeting Types: {len(self.results['touchpoint_mappings']['totango_meeting_types'])}
    Totango Touchpoint Types: {len(self.results['touchpoint_mappings']['totango_touchpoint_types'])}
    Gainsight Touchpoint Reasons: {len(self.results['touchpoint_mappings']['gainsight_reasons'])}
"""
        
        if self.results.get('flow_type_mappings'):
            report += f"""
🎯 FLOW TYPE MAPPING SUMMARY:
    Totango Flow Types: {len(self.results['flow_type_mappings']['totango_flow_types'])}
    Gainsight Flow Types: {len(self.results['flow_type_mappings']['gainsight_types'])}
"""
            
        if self.results['errors']:
            report += f"""
❌ ERRORS (first 3):
"""
            for error in self.results['errors'][:3]:
                report += f"    {error.get('account_id', 'unknown')}: {error.get('error', 'unknown')}\n"
                
        report += f"""
{'='*60}
🎉 Migration completed!
"""
        
        print(report)
    
    def save_results(self):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_dir = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram"
        
        results_file = os.path.join(base_dir, f"results_{timestamp}.json")
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        logger.info(f"Results saved: {results_file}")
    
    def debug_touchpoint_mappings(self):
        """Debug method to test touchpoint and flow type mappings"""
        logger.info("=== DEBUGGING TOUCHPOINT AND FLOW TYPE MAPPINGS ===")
        
        # Load all mapping data
        totango_touchpoint_types = self.get_totango_touchpoint_types()
        totango_flow_types = self.get_totango_flow_types()
        gainsight_touchpoint_reasons = self.get_gainsight_touchpoint_reasons()
        gainsight_flow_types = self.get_gainsight_flow_types()
        
        logger.info(f"Totango Touchpoint Types ({len(totango_touchpoint_types)}):")
        for type_id, name in list(totango_touchpoint_types.items())[:5]:
            logger.info(f"  {type_id}: {name}")
        
        logger.info(f"Totango Flow Types ({len(totango_flow_types)}):")
        for type_id, name in list(totango_flow_types.items())[:5]:
            logger.info(f"  {type_id}: {name}")
            
        logger.info(f"Gainsight Touchpoint Reasons ({len(gainsight_touchpoint_reasons)}):")
        for name, value in list(gainsight_touchpoint_reasons.items())[:5]:
            logger.info(f"  {name}: {value}")
            
        logger.info(f"Gainsight Flow Types ({len(gainsight_flow_types)}):")
        for name, value in list(gainsight_flow_types.items())[:5]:
            logger.info(f"  {name}: {value}")
        
        return {
            'totango_touchpoint_types': totango_touchpoint_types,
            'totango_flow_types': totango_flow_types,
            'gainsight_touchpoint_reasons': gainsight_touchpoint_reasons,
            'gainsight_flow_types': gainsight_flow_types
        }

def main():
    print("🚀 Enhanced Totango to Gainsight Migrator with Touchpoint & Flow Type Mapping")
    print("="*70)
    
    # Use the actual Totango account ID from the URL you provided
    account_ids = [
        '001b000003nwKk1AAE'  # ICICI Bank account from Totango
    ]
    
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    
    try:
        migrator = EnhancedTotangoGainsightMigrator(config_file)
        migrator.migrate_all_activities(account_ids)
        logger.info("Migration completed!")
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")

if __name__ == "__main__":
    main()

