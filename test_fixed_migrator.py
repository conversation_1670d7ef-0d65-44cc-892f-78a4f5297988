#!/usr/bin/env python3
"""
Test the Fixed Attachment Migrator on a Single Activity
This will test the fixes on just one activity with attachments
"""

import sys
import os
import json

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_attachment_migrator import FixedAttachmentMigrator

def test_single_activity_with_attachments():
    print("🧪 TESTING FIXED ATTACHMENT MIGRATOR")
    print("="*50)
    print("Testing on a single activity to validate the fixes...")
    print()
    
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    account_id = '001b000003nwKk1AAE'
    
    try:
        migrator = FixedAttachmentMigrator(config_file)
        
        # Get activities to find one with attachments
        print("🔍 Looking for activities with attachments...")
        activities = migrator.get_totango_activities([account_id])
        
        # Find an activity with attachments
        test_activity = None
        for activity in activities:
            properties = activity.get('properties', {})
            if properties.get('assets'):
                test_activity = activity
                print(f"✅ Found activity with assets: {activity.get('id', 'unknown')}")
                
                # Show what attachments we found
                assets = properties.get('assets', [])
                print(f"📎 Assets found: {len(assets)}")
                
                for i, asset_str in enumerate(assets[:3], 1):  # Show first 3
                    try:
                        if isinstance(asset_str, str):
                            asset = json.loads(asset_str)
                        else:
                            asset = asset_str
                        
                        asset_type = asset.get('asset_type', 'unknown')
                        asset_name = asset.get('name', 'unknown')
                        print(f"   {i}. {asset_name} (type: {asset_type})")
                        
                    except:
                        print(f"   {i}. [Parse error]")
                
                break
        
        if not test_activity:
            print("❌ No activities with attachments found")
            print("💡 Try running the main migrator anyway - it will handle activities without attachments")
            return
        
        # Test the migration on this single activity
        print()
        print("🚀 Testing migration on this activity...")
        
        # Load required data
        users = migrator.get_gainsight_users()
        company_id = migrator.config['gainsight']['company_id']
        activity_types = migrator.get_gainsight_activity_types(company_id)
        
        # Test single activity migration
        result = migrator.migrate_single_activity(test_activity, users, activity_types)
        
        print()
        print("📊 TEST RESULTS:")
        print(f"   Success: {result['success']}")
        print(f"   Activity ID: {result['activity_id']}")
        print(f"   Attachments Processed: {result.get('attachments_processed', 0)}")
        print(f"   Attachments Uploaded: {result.get('attachments_uploaded', 0)}")
        
        if result['success']:
            print("✅ TEST PASSED! The fixes are working!")
            print("🎯 You can now run the full migration with confidence.")
        else:
            print(f"❌ TEST FAILED: {result.get('error', 'Unknown error')}")
            print("🔧 Check the logs above for debugging information.")
        
        print()
        print("📈 MIGRATION STATS:")
        print(f"   Download attempts: {migrator.results['download_attempts']}")
        print(f"   Download successes: {migrator.results['download_successes']}")
        print(f"   Upload attempts: {migrator.results['upload_attempts']}")
        print(f"   Upload successes: {migrator.results['upload_successes']}")
        print(f"   Attachments uploaded: {migrator.results['attachments_uploaded']}")
        print(f"   Attachments failed: {migrator.results['attachments_failed']}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_single_activity_with_attachments()
