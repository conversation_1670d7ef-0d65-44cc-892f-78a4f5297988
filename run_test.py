#!/usr/bin/env python3
"""
🚀 Enhanced Totango to Gainsight Migration - Quick Test Run
===========================================================
Test migration with ICICI account
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Check if the main migrator exists
migrator_file = current_dir / "enhanced_totango_gainsight_migrator_complete.py"
if migrator_file.exists():
    # Import from the file
    import importlib.util
    spec = importlib.util.spec_from_file_location("migrator", migrator_file)
    migrator_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(migrator_module)
    EnhancedTotangoGainsightMigrator = migrator_module.EnhancedTotangoGainsightMigrator
else:
    print("❌ Migrator file not found!")
    sys.exit(1)

def main():
    """Run test migration"""
    print("🚀 Enhanced Totango to Gainsight Migration - Live Test")
    print("="*60)
    
    # Check config
    config_file = current_dir / "migration_config.json"
    if not config_file.exists():
        print("❌ Configuration file not found!")
        sys.exit(1)
    
    # Test with ICICI account ID
    # Based on your previous work, let's try a few common patterns
    test_account_ids = [
        'ICICI',
        'icici', 
        '1P02IPMAEL4M3CQGY4K5FHU22D2HHT11KCKU'  # Your company ID
    ]
    
    print("🎯 Test Configuration:")
    print(f"   📁 Config: {config_file}")
    print(f"   🏢 Test accounts: {test_account_ids}")
    
    try:
        # Initialize migrator
        print("\n🔧 Initializing migrator...")
        migrator = EnhancedTotangoGainsightMigrator(str(config_file))
        
        # Override for conservative testing
        migrator.config['migration']['batch_size'] = 2
        migrator.config['migration']['parallel_workers'] = 1
        migrator.config['migration']['rate_limit_delay'] = 2.0
        
        print("✅ Migrator initialized successfully")
        print(f"   📦 Batch size: {migrator.config['migration']['batch_size']}")
        print(f"   🔄 Workers: {migrator.config['migration']['parallel_workers']}")
        print(f"   ⏱️ Rate limit: {migrator.config['migration']['rate_limit_delay']}s")
        
        # Start migration
        print("\n🚀 Starting test migration...")
        print("📋 This will:")
        print("   1. Test Totango API connectivity")
        print("   2. Extract sample activities")
        print("   3. Test Gainsight API connectivity")
        print("   4. Attempt to migrate activities")
        print("   5. Generate detailed report")
        
        start_time = time.time()
        
        # Run migration
        migrator.migrate_all_activities(test_account_ids)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n🎉 Test migration completed in {duration:.1f} seconds!")
        print("📊 Check the detailed report above")
        print("📁 Log files saved in current directory")
        
    except Exception as e:
        print(f"\n❌ Test migration failed: {e}")
        print("💡 This might be due to:")
        print("   - Invalid account IDs")
        print("   - Expired session cookies")
        print("   - Network connectivity issues")
        print("   - API rate limiting")
        
        # Try to save error details
        error_file = current_dir / f"test_error_{int(time.time())}.txt"
        try:
            with open(error_file, 'w') as f:
                f.write(f"Test Error: {datetime.now()}\n")
                f.write(f"Error: {str(e)}\n")
                f.write(f"Account IDs tested: {test_account_ids}\n")
            print(f"📝 Error details saved to: {error_file}")
        except:
            pass

if __name__ == "__main__":
    main()
