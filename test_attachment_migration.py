#!/usr/bin/env python3
"""
Attachment Migration Test & Validation Script
Tests the attachment extraction and processing functionality
"""

import json
import requests
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("AttachmentTester")

class AttachmentTester:
    
    def __init__(self, config_file):
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        self.session = requests.Session()
    
    def test_totango_data_extraction(self, account_id: str):
        """Test extracting data from Totango and identify attachments"""
        logger.info(f"🧪 Testing Totango data extraction for account: {account_id}")
        
        try:
            url = f"{self.config['totango']['url']}/t01/mend/api/v2/events/"
            params = {
                'account_id': account_id,
                'include_formatting': 'true'
            }
            
            response = self.session.get(
                url,
                headers=self.config['totango']['headers'],
                params=params,
                timeout=30
            )
            
            if response.status_code == 200:
                events = response.json()
                
                attachment_stats = {
                    'total_events': len(events),
                    'events_with_meeting_type': 0,
                    'events_with_assets': 0,
                    'total_assets': 0,
                    'asset_types': {},
                    'sample_assets': []
                }
                
                for event in events:
                    properties = event.get('properties', {})
                    
                    # Check for meeting type
                    if 'meeting_type' in properties and properties['meeting_type']:
                        attachment_stats['events_with_meeting_type'] += 1
                        
                        # Check for assets
                        assets = properties.get('assets', [])
                        if assets:
                            attachment_stats['events_with_assets'] += 1
                            attachment_stats['total_assets'] += len(assets)
                            
                            # Analyze asset types
                            for asset_str in assets:
                                try:
                                    if isinstance(asset_str, str):
                                        asset = json.loads(asset_str)
                                    else:
                                        asset = asset_str
                                    
                                    asset_type = asset.get('asset_type', 'unknown')
                                    attachment_stats['asset_types'][asset_type] = attachment_stats['asset_types'].get(asset_type, 0) + 1
                                    
                                    # Keep sample for inspection
                                    if len(attachment_stats['sample_assets']) < 5:
                                        attachment_stats['sample_assets'].append({
                                            'name': asset.get('name', 'Unknown'),
                                            'type': asset_type,
                                            'has_url': bool(asset.get('asset_url')),
                                            'has_download_path': bool(asset.get('downloadPath')),
                                            'extension': asset.get('extension', '')
                                        })
                                
                                except Exception as e:
                                    logger.error(f"Error parsing asset: {e}")
                
                # Print results
                logger.info("📊 TOTANGO DATA ANALYSIS:")
                logger.info(f"  Total Events: {attachment_stats['total_events']}")
                logger.info(f"  Events with meeting_type: {attachment_stats['events_with_meeting_type']}")
                logger.info(f"  Events with assets: {attachment_stats['events_with_assets']}")
                logger.info(f"  Total assets found: {attachment_stats['total_assets']}")
                logger.info(f"  Asset types: {attachment_stats['asset_types']}")
                
                if attachment_stats['sample_assets']:
                    logger.info("📎 SAMPLE ASSETS:")
                    for i, asset in enumerate(attachment_stats['sample_assets'], 1):
                        logger.info(f"    {i}. {asset['name']} ({asset['type']}) - URL: {asset['has_url']}, Download: {asset['has_download_path']}")
                
                return attachment_stats
                
            else:
                logger.error(f"❌ Failed to fetch Totango data: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error testing Totango extraction: {e}")
            return None
    
    def test_gainsight_attachment_api(self):
        """Test Gainsight attachment API connectivity"""
        logger.info("🧪 Testing Gainsight attachment API connectivity...")
        
        try:
            # Test basic connectivity to the attachment endpoint
            url = f"{self.config['gainsight']['url']}/v1/ant/attachments"
            
            # Create a small test file
            test_content = b"Test attachment content for API validation"
            
            files = {
                'file': ('test.txt', test_content, 'text/plain')
            }
            
            form_data = {
                'contexts': json.dumps([{
                    'id': self.config['gainsight']['company_id'],
                    'base': True,
                    'obj': 'Company',
                    'lbl': self.config['gainsight']['company_name'],
                    'eid': None,
                    'eobj': 'Account',
                    'eurl': None,
                    'esys': 'SALESFORCE',
                    'dsp': True
                }]),
                'source': 'C360'
            }
            
            # Create headers without Content-Type (requests will set it for multipart)
            upload_headers = {k: v for k, v in self.config['gainsight']['headers'].items() 
                            if k.lower() != 'content-type'}
            
            response = self.session.post(
                url,
                files=files,
                data=form_data,
                headers=upload_headers,
                timeout=60
            )
            
            logger.info(f"📡 Gainsight API Response: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                attachment_id = result.get('data', {}).get('id')
                logger.info(f"✅ Test upload successful! Attachment ID: {attachment_id}")
                return True
            else:
                logger.error(f"❌ Test upload failed: {response.status_code}")
                logger.error(f"Response: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error testing Gainsight API: {e}")
            return False
    
    def test_attachment_processing_workflow(self, account_id: str):
        """Test the complete attachment processing workflow"""
        logger.info("🧪 Testing complete attachment processing workflow...")
        
        # Step 1: Extract sample data
        logger.info("Step 1: Extracting sample data...")
        stats = self.test_totango_data_extraction(account_id)
        
        if not stats or stats['total_assets'] == 0:
            logger.warning("⚠️ No attachments found to test processing workflow")
            return False
        
        # Step 2: Test Gainsight API
        logger.info("Step 2: Testing Gainsight API...")
        api_test = self.test_gainsight_attachment_api()
        
        if not api_test:
            logger.error("❌ Gainsight API test failed - check your credentials and permissions")
            return False
        
        logger.info("✅ Attachment processing workflow test completed successfully!")
        logger.info(f"📈 Ready to process {stats['total_assets']} attachments from {stats['events_with_assets']} activities")
        
        return True
    
    def validate_configuration(self):
        """Validate the migration configuration"""
        logger.info("🧪 Validating migration configuration...")
        
        required_totango_fields = ['url', 'headers']
        required_gainsight_fields = ['url', 'headers', 'company_id', 'company_name', 'user_id', 'user_name', 'user_email']
        
        # Check Totango config
        totango_config = self.config.get('totango', {})
        missing_totango = [field for field in required_totango_fields if not totango_config.get(field)]
        
        # Check Gainsight config
        gainsight_config = self.config.get('gainsight', {})
        missing_gainsight = [field for field in required_gainsight_fields if not gainsight_config.get(field)]
        
        # Report results
        if missing_totango:
            logger.error(f"❌ Missing Totango config fields: {missing_totango}")
        else:
            logger.info("✅ Totango configuration is complete")
        
        if missing_gainsight:
            logger.error(f"❌ Missing Gainsight config fields: {missing_gainsight}")
        else:
            logger.info("✅ Gainsight configuration is complete")
        
        if not missing_totango and not missing_gainsight:
            logger.info("✅ All configuration fields are present")
            return True
        else:
            logger.error("❌ Configuration validation failed")
            return False

def main():
    print("🧪 ATTACHMENT MIGRATION TESTER")
    print("="*50)
    
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    account_id = "001b000003nwKk1AAE"  # ICICI Bank account
    
    try:
        tester = AttachmentTester(config_file)
        
        # Run all tests
        print("\n1. CONFIGURATION VALIDATION")
        print("-" * 30)
        config_valid = tester.validate_configuration()
        
        print("\n2. TOTANGO DATA EXTRACTION TEST")
        print("-" * 35)
        stats = tester.test_totango_data_extraction(account_id)
        
        print("\n3. GAINSIGHT API CONNECTIVITY TEST")
        print("-" * 38)
        api_test = tester.test_gainsight_attachment_api()
        
        print("\n4. COMPLETE WORKFLOW TEST")
        print("-" * 28)
        workflow_test = tester.test_attachment_processing_workflow(account_id)
        
        # Summary
        print("\n" + "="*50)
        print("🎯 TEST SUMMARY")
        print("="*50)
        print(f"✅ Configuration: {'PASS' if config_valid else 'FAIL'}")
        print(f"✅ Totango Data: {'PASS' if stats else 'FAIL'}")
        print(f"✅ Gainsight API: {'PASS' if api_test else 'FAIL'}")
        print(f"✅ Full Workflow: {'PASS' if workflow_test else 'FAIL'}")
        
        if config_valid and stats and api_test and workflow_test:
            print("\n🎉 ALL TESTS PASSED! Ready for attachment migration!")
        else:
            print("\n⚠️ Some tests failed. Please review the errors above.")
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")

if __name__ == "__main__":
    main()
