#!/usr/bin/env python3
"""
Test script for touchpoint reason and flow type mapping functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_totango_gainsight_migrator_complete import EnhancedTotangoGainsightMigrator
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)

def test_touchpoint_mappings():
    """Test the new touchpoint and flow type mapping functionality"""
    print("🧪 Testing Touchpoint and Flow Type Mapping")
    print("="*50)
    
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    
    try:
        # Initialize migrator
        migrator = EnhancedTotangoGainsightMigrator(config_file)
        
        # Test the mapping functionality
        mappings = migrator.debug_touchpoint_mappings()
        
        print("\n✅ Mapping Test Results:")
        print(f"   Totango Touchpoint Types: {len(mappings['totango_touchpoint_types'])}")
        print(f"   Totango Flow Types: {len(mappings['totango_flow_types'])}")
        print(f"   Gainsight Touchpoint Reasons: {len(mappings['gainsight_touchpoint_reasons'])}")
        print(f"   Gainsight Flow Types: {len(mappings['gainsight_flow_types'])}")
        
        # Test mapping logic with sample data
        print("\n🔍 Testing Mapping Logic:")
        
        # Sample Totango activity for testing
        sample_activity = {
            'properties': {
                'meeting_type': 'Web Meeting',
                'subject': 'Product Demo for Q1 Review',
                'touchpoint_type_id': 'demo_id_123',
                'flow_type_id': 'outbound_id_456'
            },
            'note_content': {
                'text': 'Conducted product demonstration for the client'
            }
        }
        
        # Test touchpoint reason mapping
        touchpoint_reason = migrator.map_touchpoint_reason(
            sample_activity, 
            mappings['gainsight_touchpoint_reasons']
        )
        print(f"   Sample Touchpoint Reason: {touchpoint_reason}")
        
        # Test flow type mapping
        flow_type = migrator.map_flow_type(
            sample_activity,
            mappings['gainsight_flow_types']
        )
        print(f"   Sample Flow Type: {flow_type}")
        
        print("\n🎉 Touchpoint mapping test completed successfully!")
        print("Your enhanced migrator now includes touchpoint reason and flow type mapping")
        print("that your friend's code was missing!")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_touchpoint_mappings()
    if success:
        print("\n✅ All tests passed! Ready for migration with enhanced touchpoint mapping.")
    else:
        print("\n❌ Tests failed. Please check configuration and API access.")
