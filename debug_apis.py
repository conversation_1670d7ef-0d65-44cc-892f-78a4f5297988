#!/usr/bin/env python3
"""
Debug Gainsight API - Test Activity Types Loading
"""

import requests
import json
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("Debug")

def test_gainsight_activity_types():
    # Load config
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    session = requests.Session()
    company_id = config['gainsight']['company_id']
    
    logger.info(f"Testing activity types for company: {company_id}")
    
    try:
        url = f"{config['gainsight']['url']}/v1/ant/forms"
        params = {
            'context': 'Company',
            'contextId': company_id,
            'showHidden': 'false'
        }
        
        logger.info(f"URL: {url}")
        logger.info(f"Params: {params}")
        
        response = session.get(
            url,
            params=params,
            headers=config['gainsight']['headers'],
            timeout=30
        )
        
        logger.info(f"Status Code: {response.status_code}")
        logger.info(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            logger.info(f"Response keys: {list(data.keys())}")
            
            if 'data' in data:
                data_content = data['data']
                logger.info(f"Data keys: {list(data_content.keys())}")
                
                if 'activityTypes' in data_content:
                    activity_types = data_content['activityTypes']
                    logger.info(f"Found {len(activity_types)} activity types:")
                    
                    for i, activity_type in enumerate(activity_types[:5]):  # Show first 5
                        name = activity_type.get('name', 'No name')
                        type_id = activity_type.get('id', 'No ID')
                        logger.info(f"  {i+1}. {name}: {type_id}")
                        
                    # Save full response for inspection
                    with open("/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/debug_activity_types.json", 'w') as f:
                        json.dump(data, f, indent=2)
                    logger.info("Full response saved to debug_activity_types.json")
                    
                else:
                    logger.error("No 'activityTypes' found in data")
                    logger.info(f"Available data keys: {list(data_content.keys())}")
            else:
                logger.error("No 'data' found in response")
                logger.info(f"Available keys: {list(data.keys())}")
        else:
            logger.error(f"Failed with status {response.status_code}")
            logger.error(f"Response: {response.text[:500]}")
            
    except Exception as e:
        logger.error(f"Exception: {e}")

def test_totango_activities():
    # Load config
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    session = requests.Session()
    account_id = '0015p00005R7ysqAAB'
    
    logger.info(f"Testing Totango activities for account: {account_id}")
    
    try:
        url = f"{config['totango']['url']}/t01/mend/api/v2/events/"
        params = {
            'account_id': account_id,
            'include_formatting': 'true'
        }
        
        response = session.get(
            url,
            headers=config['totango']['headers'],
            params=params,
            timeout=30
        )
        
        logger.info(f"Totango Status Code: {response.status_code}")
        
        if response.status_code == 200:
            events = response.json()
            logger.info(f"Found {len(events)} total events")
            
            # Analyze event types
            event_types = {}
            meeting_types = {}
            events_with_note_content = 0
            
            for event in events:
                event_type = event.get('type', 'unknown')
                event_types[event_type] = event_types.get(event_type, 0) + 1
                
                properties = event.get('properties', {})
                if 'meeting_type' in properties:
                    mt = properties['meeting_type']
                    meeting_types[mt] = meeting_types.get(mt, 0) + 1
                
                if 'note_content' in event and event['note_content']:
                    events_with_note_content += 1
            
            logger.info(f"Event types: {event_types}")
            logger.info(f"Meeting types: {meeting_types}")
            logger.info(f"Events with note_content: {events_with_note_content}")
            
            # Show first few events
            logger.info("First 3 events:")
            for i, event in enumerate(events[:3]):
                logger.info(f"  Event {i+1}:")
                logger.info(f"    Type: {event.get('type', 'unknown')}")
                logger.info(f"    ID: {event.get('id', 'unknown')}")
                if 'properties' in event:
                    props = event['properties']
                    logger.info(f"    Properties keys: {list(props.keys())}")
                    if 'meeting_type' in props:
                        logger.info(f"    Meeting type: {props['meeting_type']}")
                
        else:
            logger.error(f"Totango failed with status {response.status_code}")
            logger.error(f"Response: {response.text[:500]}")
            
    except Exception as e:
        logger.error(f"Totango exception: {e}")

if __name__ == "__main__":
    print("🔍 Testing Gainsight Activity Types...")
    test_gainsight_activity_types()
    
    print("\n🔍 Testing Totango Activities...")
    test_totango_activities()
