# Enhancement patch for Gainsight attachment upload issue in Totango to Gainsight migration script

import requests
import os
import json
import logging

# Setup logger
logger = logging.getLogger("AttachmentUploader")
logger.setLevel(logging.INFO)


def download_attachment(download_url: str, local_filename: str) -> str:
    """
    Downloads the attachment from Totango to a local file.
    """
    try:
        response = requests.get(download_url, stream=True)
        response.raise_for_status()
        with open(local_filename, "wb") as f:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    f.write(chunk)
        logger.info(f"Downloaded attachment to {local_filename}")
        return local_filename
    except Exception as e:
        logger.error(f"Error downloading attachment: {e}")
        return None


def upload_to_gainsight_attachment_api(file_path: str, filename: str, gs_api_token: str) -> dict:
    """
    Uploads a file to Gainsight using the attachment API and returns the uploaded file metadata.
    """
    try:
        api_url = "https://demo-emea1.gainsightcloud.com/v1/ant/attachments"
        headers = {
            "Authorization": f"Bearer {gs_api_token}"
        }
        files = {
            'file': (filename, open(file_path, 'rb'))
        }
        response = requests.post(api_url, headers=headers, files=files)
        response.raise_for_status()
        logger.info(f"Uploaded file {filename} to Gainsight")
        return response.json().get("data", {})
    except Exception as e:
        logger.error(f"Error uploading attachment to Gainsight: {e}")
        return {}


# Example usage
if __name__ == "__main__":
    # Input from Totango JSON
    totango_attachment = {
        "name": "IBM.pptx",
        "downloadPath": "/45606/account/001b000003nwKk1AAE/uploads/eYgz_4YBQNQJt_S2EfUE"
    }
    tokenized_download_url = "https://assets-proxy.totango.com/api/v2/assets" + totango_attachment["downloadPath"] + "?token=YOUR_VALID_TOTANGO_DOWNLOAD_TOKEN"
    gs_api_token = os.getenv("GAINSIGHT_API_TOKEN")

    # Step 1: Download from Totango
    local_file = download_attachment(tokenized_download_url, totango_attachment["name"])

    # Step 2: Upload to Gainsight
    if local_file:
        upload_metadata = upload_to_gainsight_attachment_api(local_file, totango_attachment["name"], gs_api_token)
        print("Attachment uploaded. Metadata:", json.dumps(upload_metadata, indent=2))

        # Optional: Clean up local file
        os.remove(local_file)
