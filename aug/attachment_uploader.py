# FIXED Enhancement patch for Gainsight attachment upload issue in Totango to Gainsight migration script
# This addresses the key issues found in enhanced_attachment_migrator.py

import requests
import os
import json
import logging
import base64
from typing import Optional, Dict

# Setup logger
logger = logging.getLogger("AttachmentUploader")
logger.setLevel(logging.INFO)


class FixedAttachmentUploader:
    """
    Fixed attachment uploader that addresses the issues in the main migration script
    """

    def __init__(self, config: Dict):
        self.config = config
        self.session = requests.Session()

    def get_jwt_token_from_config(self) -> Optional[str]:
        """
        Extract JWT token from config or headers - FIXED VERSION
        """
        try:
            # Priority 1: Manual token in config
            manual_token = self.config.get('totango', {}).get('jwt_token', '')
            if manual_token and len(manual_token) > 50:
                logger.info("🔑 Using manually configured JWT token")
                return manual_token

            # Priority 2: Extract from headers
            headers = self.config.get('totango', {}).get('headers', {})

            # Check Authorization header
            auth_header = headers.get('Authorization', '')
            if 'Bearer ' in auth_header:
                token = auth_header.replace('Bearer ', '')
                if self.is_valid_jwt(token):
                    logger.info("🔑 Found valid JWT in Authorization header")
                    return token

            # Check cookies for JWT patterns
            cookie_header = headers.get('Cookie', '')
            if cookie_header:
                import re
                jwt_pattern = r'[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+'
                matches = re.findall(jwt_pattern, cookie_header)

                for match in matches:
                    if len(match) > 100 and self.is_valid_jwt(match):
                        logger.info("🔑 Found valid JWT in cookies")
                        return match

            logger.error("❌ No valid JWT token found in config or headers")
            return None

        except Exception as e:
            logger.error(f"❌ Error extracting JWT token: {e}")
            return None

    def is_valid_jwt(self, token: str) -> bool:
        """Check if a string is a valid JWT token"""
        if not token or len(token) < 50:
            return False

        parts = token.split('.')
        if len(parts) != 3:
            return False

        try:
            header = parts[0]
            header += '=' * (4 - len(header) % 4)
            decoded_header = base64.b64decode(header, validate=True)
            header_json = json.loads(decoded_header)
            return 'alg' in header_json and 'typ' in header_json
        except:
            return False

    def download_from_totango_with_token(self, download_path: str, filename: str) -> Optional[bytes]:
        """
        FIXED: Download attachment from Totango using proper JWT token authentication
        """
        try:
            # Get JWT token
            token = self.get_jwt_token_from_config()
            if not token:
                logger.error(f"❌ No JWT token available for {filename}")
                return None

            # Construct assets-proxy URL with token
            path_parts = download_path.strip('/').split('/')
            if len(path_parts) < 2:
                logger.error(f"❌ Invalid download path format: {download_path}")
                return None

            service_id = path_parts[0]
            file_path = '/'.join(path_parts[1:])

            download_url = f"https://assets-proxy.totango.com/api/v2/assets/{service_id}/{file_path}?token={token}"

            logger.info(f"📥 Downloading {filename} from Totango...")
            logger.info(f"🔗 URL: {download_url[:100]}...")

            response = self.session.get(download_url, timeout=60, stream=True)

            if response.status_code == 200:
                content = response.content
                logger.info(f"✅ Downloaded {len(content)} bytes for {filename}")
                return content
            elif response.status_code == 401:
                logger.error(f"❌ JWT token expired or invalid for {filename}")
                return None
            elif response.status_code == 404:
                logger.error(f"❌ File not found: {filename}")
                return None
            else:
                logger.error(f"❌ Download failed with status {response.status_code} for {filename}")
                return None

        except Exception as e:
            logger.error(f"❌ Error downloading {filename}: {e}")
            return None

    def get_mime_type(self, extension: str) -> str:
        """Get MIME type for file extension"""
        extension = extension.lower().lstrip('.')

        mime_types = {
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt': 'application/vnd.ms-powerpoint',
            'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'gif': 'image/gif',
            'bmp': 'image/bmp',
            'txt': 'text/plain',
            'csv': 'text/csv',
            'json': 'application/json',
            'zip': 'application/zip',
            'rar': 'application/x-rar-compressed'
        }

        return mime_types.get(extension, 'application/octet-stream')

    def upload_to_gainsight_fixed(self, filename: str, file_content: bytes, extension: str,
                                  activity_id: str = None) -> Optional[str]:
        """
        FIXED: Upload attachment to Gainsight using the correct endpoint and format
        """
        try:
            logger.info(f"📤 Uploading {filename} to Gainsight...")
            logger.info(f"  File size: {len(file_content)} bytes")
            logger.info(f"  MIME type: {self.get_mime_type(extension)}")

            # Prepare multipart form data
            files = {
                'file': (filename, file_content, self.get_mime_type(extension))
            }

            # FIXED: Use the correct form data structure based on working examples
            form_data = {
                'contexts': json.dumps([{
                    'id': self.config['gainsight']['company_id'],
                    'base': True,
                    'obj': 'Company',
                    'lbl': self.config['gainsight']['company_name'],
                    'eid': None,
                    'eobj': 'Account',
                    'eurl': None,
                    'esys': 'SALESFORCE',
                    'dsp': True
                }]),
                'source': 'C360'
            }

            # Add activity ID if provided
            if activity_id:
                form_data['entityId'] = activity_id

            # FIXED: Use the correct endpoint (test both to see which works)
            upload_url = f"{self.config['gainsight']['url']}/v1/ant/attachments"

            # Prepare headers (remove content-type to let requests handle multipart)
            upload_headers = {}
            for key, value in self.config['gainsight']['headers'].items():
                if key.lower() not in ['content-type', 'content-length']:
                    upload_headers[key] = value

            logger.info(f"📡 Uploading to: {upload_url}")

            # Make the upload request
            response = self.session.post(
                upload_url,
                files=files,
                data=form_data,
                headers=upload_headers,
                timeout=120
            )

            logger.info(f"📥 Upload response: {response.status_code}")

            if response.status_code == 200:
                try:
                    result = response.json()

                    if result.get('result') and result.get('data'):
                        attachment_data = result['data']
                        attachment_id = attachment_data.get('id')

                        if attachment_id:
                            logger.info(f"✅ Upload successful! Attachment ID: {attachment_id}")
                            return attachment_id
                        else:
                            logger.error(f"❌ No attachment ID in response")
                            logger.error(f"Response data: {attachment_data}")
                    else:
                        logger.error(f"❌ Unexpected response structure: {result}")

                except json.JSONDecodeError as e:
                    logger.error(f"❌ Invalid JSON response: {e}")
                    logger.error(f"Response text: {response.text[:500]}")

            else:
                logger.error(f"❌ Upload failed: {response.status_code}")
                logger.error(f"Response: {response.text[:500]}")

            return None

        except Exception as e:
            logger.error(f"❌ Error uploading to Gainsight: {e}")
            return None

    def process_attachment_migration(self, totango_attachment: Dict, activity_id: str = None) -> Optional[str]:
        """
        Complete workflow: Download from Totango and upload to Gainsight
        """
        try:
            name = totango_attachment.get('name', 'attachment')
            download_path = totango_attachment.get('downloadPath', '')
            extension = totango_attachment.get('extension', '')

            if not download_path:
                logger.error(f"❌ No download path for {name}")
                return None

            logger.info(f"🔄 Processing attachment: {name}")

            # Step 1: Download from Totango
            file_content = self.download_from_totango_with_token(download_path, name)
            if not file_content:
                logger.error(f"❌ Failed to download {name}")
                return None

            # Step 2: Upload to Gainsight
            attachment_id = self.upload_to_gainsight_fixed(name, file_content, extension, activity_id)
            if attachment_id:
                logger.info(f"✅ Successfully migrated {name} -> Attachment ID: {attachment_id}")
                return attachment_id
            else:
                logger.error(f"❌ Failed to upload {name} to Gainsight")
                return None

        except Exception as e:
            logger.error(f"❌ Error processing attachment migration: {e}")
            return None


# Example usage and testing functions
def test_jwt_extraction(config: Dict):
    """Test JWT token extraction from config"""
    uploader = FixedAttachmentUploader(config)
    token = uploader.get_jwt_token_from_config()

    if token:
        print(f"✅ JWT Token found: {token[:50]}...")
        print(f"   Token length: {len(token)}")
        print(f"   Valid JWT: {uploader.is_valid_jwt(token)}")
    else:
        print("❌ No JWT token found")

    return token


def test_attachment_download(config: Dict, download_path: str, filename: str):
    """Test downloading a single attachment"""
    uploader = FixedAttachmentUploader(config)
    content = uploader.download_from_totango_with_token(download_path, filename)

    if content:
        print(f"✅ Downloaded {len(content)} bytes for {filename}")
        return content
    else:
        print(f"❌ Failed to download {filename}")
        return None


def test_gainsight_upload(config: Dict, filename: str, file_content: bytes, extension: str):
    """Test uploading to Gainsight"""
    uploader = FixedAttachmentUploader(config)
    attachment_id = uploader.upload_to_gainsight_fixed(filename, file_content, extension)

    if attachment_id:
        print(f"✅ Uploaded {filename} -> ID: {attachment_id}")
        return attachment_id
    else:
        print(f"❌ Failed to upload {filename}")
        return None


if __name__ == "__main__":
    # Example configuration structure
    example_config = {
        'totango': {
            'url': 'https://app.totango.com',
            'headers': {
                'Authorization': 'Bearer YOUR_TOTANGO_TOKEN',
                'Cookie': 'your_session_cookies_here'
            },
            'jwt_token': 'YOUR_JWT_TOKEN_HERE'  # Optional: manual token
        },
        'gainsight': {
            'url': 'https://demo-emea1.gainsightcloud.com',
            'company_id': 'YOUR_COMPANY_ID',
            'company_name': 'Your Company Name',
            'headers': {
                'Cookie': 'your_gainsight_session_cookies'
            }
        }
    }

    # Example Totango attachment from your data
    example_attachment = {
        "name": "IBM.pptx",
        "downloadPath": "/45606/account/001b000003nwKk1AAE/uploads/eYgz_4YBQNQJt_S2EfUE",
        "extension": "pptx"
    }

    print("🧪 TESTING FIXED ATTACHMENT UPLOADER")
    print("=" * 50)

    # Test 1: JWT Token extraction
    print("\n1. Testing JWT token extraction...")
    test_jwt_extraction(example_config)

    # Test 2: Download test (uncomment when you have real config)
    # print("\n2. Testing download...")
    # test_attachment_download(example_config, example_attachment['downloadPath'], example_attachment['name'])

    # Test 3: Full migration test (uncomment when ready)
    # print("\n3. Testing full migration...")
    # uploader = FixedAttachmentUploader(example_config)
    # result = uploader.process_attachment_migration(example_attachment)
    # print(f"Migration result: {result}")

    print("\n✅ Testing complete. Update the config with your real values to test with actual data.")
