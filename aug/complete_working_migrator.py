#!/usr/bin/env python3
"""
COMPLETE WORKING Totango to Gainsight Migrator with FIXED ATTACHMENT SUPPORT
This version fixes all the issues found in the original enhanced_attachment_migrator.py
"""

import requests
import json
import time
import logging
import os
import re
import base64
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from collections import defaultdict
from urllib.parse import urlparse, unquote

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("migration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("CompleteTotangoMigrator")

class CompleteTotangoGainsightMigrator:
    
    def __init__(self, config_file=None):
        self.config = self.load_config(config_file)
        self.session = requests.Session()
        self.results = {
            'success': 0,
            'failed': 0,
            'errors': [],
            'activity_types': {},
            'attachments_processed': 0,
            'attachments_uploaded': 0,
            'attachments_failed': 0,
            'mappings_applied': {
                'touchpoint_reason_mappings': 0,
                'flow_type_mappings': 0
            }
        }
        
        # Direct ID mappings from your configuration
        self.touchpoint_id_mapping = {
            "12980f6d-797c-47da-ab58-78f3a3e5c954": "1I00J1INQCQ6GQ3T2MID7L1S4X8HPJ2OP95B",  # SCANNER MAINTENANCE
            "16a15fcd-d735-49a7-8b14-ca279e6dba5a": "1I00J1INQCQ6GQ3T2MOPQWUV1NFURNPEK8VE",  # MEND CONTAINER
            "30989b43-ff62-486c-b4db-f89c3106abba": "1I00J1INQCQ6GQ3T2MWULITYE9ASDFPQ2KD3",  # ACCOUNT REVIEW
            "38bb62d0-4b50-4a5f-906c-6edbfe4786db": "1I00J1INQCQ6GQ3T2MYNG8BC6SJCCN7SAPKC",  # MEND SCA
            "38ce7c4e-374f-42db-94e8-23ba48d4b1e3": "1I00J1INQCQ6GQ3T2MTA6WH5AKWS5JL0O87R",  # ESCALATION
            "6cc323e4-4155-48bf-964c-ec75ee70e96b": "1I00J1INQCQ6GQ3T2MGRUSTP2XZGZBXN5263",  # MEND PLATFORM ACCESS
            "7c285778-76be-4543-b8a1-9066ee5a1ad4": "1I00J1INQCQ6GQ3T2M0O5F16H63MEMNDN97J",  # MEND SAST
            "9165fe90-5bc2-480d-98b2-76f3926c8e23": "1I00J1INQCQ6GQ3T2MY27A9SQHEE253247F2",  # SUPPORT
            "a2d4c216-8d7b-4f24-9cdf-3c4bc0226f94": "1I00J1INQCQ6GQ3T2MJY0GQI8EZ9STP11VR2",  # MARKETING
            "b1120b9a-85e9-4c2c-b7e7-9214ac3baaea": "1I00J1INQCQ6GQ3T2M3D94LFFY2R8UMNJXE0",  # NPS
            "c5d4cd04-1224-4ad3-8696-d2fb70ea85be": "1I00J1INQCQ6GQ3T2MXT1PJXBF3KATW6QR4K",  # PRODUCT MANAGEMENT ENGAGEMENT
            "cbd1f1f4-229a-4e1b-b4a3-a091e90932cc": "1I00J1INQCQ6GQ3T2M6MLMI12JOYH1Z05ILP",  # ONBOARDING
            "ceb3f746-7099-4083-bc18-7d941699c6b5": "1I00J1INQCQ6GQ3T2M9VQOCBJ5RVU2YW5V4B",  # REACHABILITY (SCA)
            "cefe339e-50d6-434d-a126-d66d652ce06c": "1I00J1INQCQ6GQ3T2MENNOYNKWB0ZEM5WII6",  # MEND AI
            "ddf60bee-a1d2-4b03-b1bd-1f59eef0e214": "1I00J1INQCQ6GQ3T2M2M84A559MT05AWXZT7",  # OUTAGE
            "e43ae486-e0b8-4e2c-a8f3-fe17821b003d": "1I00J1INQCQ6GQ3T2MRYA87NSXMM46FZCUZH",  # CADENCE
            "e4b6a6cf-0833-4a60-aac2-52759e73e9ab": "1I00J1INQCQ6GQ3T2M16QWTIDVSRJTOA8219",  # RENEWAL
            "eb5d8037-0f63-452e-8007-977e91e061b5": "1I00J1INQCQ6GQ3T2MZKWMBM3ZATASUU2VFS",  # REFERENCE
            "ed358b0e-5cad-42e6-80be-04df0db7dc2c": "1I00J1INQCQ6GQ3T2MQN0KLL5SPTQZYCWWG6",  # FEE
            "f9f8cc62-1436-4f96-afe7-ef77a2ff18ed": "1I00J1INQCQ6GQ3T2MNUINIM89ZGC48DOXQR"   # SAST CLOUD MIGRATION
        }
        
        # Direct flow type mappings
        self.flow_type_id_mapping = {
            "renewal": "1I00EBMOY66ROGCBY6844WMEDZF8B8RXHTP1",
            "support": "1I00EBMOY66ROGCBY66HJSD9I0AG8E8Q1A0D",
            "upsell": "1I00EBMOY66ROGCBY6J9O3U5YYU9F5NDTN2X",
            "escalation": "1I00EBMOY66ROGCBY6L0K6AGIJF5FPS2BATD",
            "onboarding_101": "1I00EBMOY66ROGCBY63I51PAXG5OJRPK37AQ",
            "adoption": "1I00EBMOY66ROGCBY6IJJ2GVGY81F62M23Z6",
            "risk_1560979263618": "1I00EBMOY66ROGCBY6YKHPSNQCJ382C3YQQU",
            "product_transition_1630456786595": "1I00EBMOY66ROGCBY6F8BZABGHFA7Z1OOYW5",
            "intelligence_1561140678082": "1I00EBMOY66ROGCBY6H0LHB0SGQLAU3N58O4",
            "services_1631204797082": "1I00EBMOY66ROGCBY650ON17KW7GYGD2H5ZY",
            "inbound_1631240727463": "1I00EBMOY66ROGCBY6YAF5US4GK95QEO5ZFU",
            "design_partner_1635451768576": "1I00EBMOY66ROGCBY6F8NQVJY43GPGH1XTD8",
            "nps_1654203738879": "1I00EBMOY66ROGCBY68KLFLZ3KPM5GGP9YUH",
            "business_review_1628207371592": "1I00EBMOY66ROGCBY6EOK9JO7C3HUUM9O5F7",
            "journey_1713548309375": "1I00EBMOY66ROGCBY6QL90GOMJA5GILCWWLT",
            "lifecycle_1714094340032": "1I00EBMOY66ROGCBY63E4BLUKMID71508EXR"
        }
    
    def load_config(self, config_file):
        """Load configuration from file or return empty dict"""
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r') as f:
                return json.load(f)
        return {}
    
    def get_totango_activities(self, account_ids: List[str]) -> List[Dict]:
        """Extract activities with meeting_type from Totango accounts"""
        logger.info(f"Extracting activities with meeting_type from {len(account_ids)} Totango accounts...")
        
        all_activities = []
        
        for account_id in account_ids:
            try:
                url = f"{self.config['totango']['url']}/t01/mend/api/v2/events/"
                params = {
                    'account_id': account_id,
                    'include_formatting': 'true'
                }
                
                response = self.session.get(
                    url,
                    headers=self.config['totango']['headers'],
                    params=params,
                    timeout=30
                )
                
                if response.status_code == 200:
                    events = response.json()
                    
                    # Filter for events with meeting_type property ONLY
                    meeting_events = []
                    for event in events:
                        properties = event.get('properties', {})
                        if 'meeting_type' in properties and properties['meeting_type']:
                            event['sourceAccountId'] = account_id
                            meeting_events.append(event)
                    
                    all_activities.extend(meeting_events)
                    logger.info(f"Account {account_id}: {len(meeting_events)} activities with meeting_type")
                    
                else:
                    logger.error(f"Failed for account {account_id}: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"Error with account {account_id}: {e}")
                
        logger.info(f"Total activities with meeting_type extracted: {len(all_activities)}")
        return all_activities
    
    def get_gainsight_users(self) -> Dict[str, Dict]:
        """Load Gainsight users"""
        logger.info("Loading Gainsight users...")
        
        users = {}
        try:
            all_users = []
            page_number = 1
            
            while True:
                payload = {
                    'limit': 50,
                    'pageNumber': page_number,
                    'searchString': '',
                    'clause': None,
                    'fields': ['Email', 'Gsid', 'Name']
                }
                
                response = self.session.post(
                    f"{self.config['gainsight']['url']}/v1/dataops/gdm/list?object=GsUser",
                    json=payload,
                    headers=self.config['gainsight']['headers'],
                    timeout=30
                )
                
                if response.status_code != 200:
                    break
                    
                users_data = response.json().get('data', {}).get('data', [])
                if not users_data:
                    break
                    
                all_users.extend(users_data)
                page_number += 1
                
            for user in all_users:
                email = user.get('Email', '').lower()
                if email:
                    users[email] = {
                        'id': user.get('Gsid', ''),
                        'name': user.get('Name', ''),
                        'email': user.get('Email', '')
                    }
                    
            logger.info(f"Loaded {len(users)} users")
            
        except Exception as e:
            logger.error(f"Failed to load users: {e}")
            
        return users
    
    def get_gainsight_activity_types(self, company_id: str) -> Dict[str, str]:
        """Load activity types from Gainsight"""
        logger.info("Loading activity types...")
        
        try:
            url = f"{self.config['gainsight']['url']}/v1/ant/forms"
            params = {
                'context': 'Company',
                'contextId': company_id,
                'showHidden': 'false'
            }
            
            response = self.session.get(
                url,
                params=params,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data and 'activityTypes' in data['data']:
                    activity_types = data['data']['activityTypes']
                    
                    type_mapping = {}
                    for activity_type in activity_types:
                        name = activity_type.get('name', '').upper()
                        type_id = activity_type.get('id', '')
                        if name and type_id:
                            type_mapping[name] = type_id
                    
                    logger.info(f"Successfully loaded {len(type_mapping)} activity types")
                    self.results['activity_types'] = type_mapping
                    
                    return type_mapping
                else:
                    logger.error(f"Unexpected response structure. Keys: {list(data.keys())}")
                    
            else:
                logger.error(f"API call failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Exception loading activity types: {e}")

        return {}

    # ============================================================================
    # FIXED ATTACHMENT HANDLING METHODS
    # ============================================================================

    def extract_totango_attachments(self, activity: Dict) -> List[Dict]:
        """Extract and parse attachments from Totango activity"""
        attachments = []

        try:
            properties = activity.get('properties', {})
            assets = properties.get('assets', [])

            if not assets:
                return attachments

            logger.info(f"🔗 Processing {len(assets)} assets from activity")

            for asset_str in assets:
                try:
                    # Parse the JSON string
                    if isinstance(asset_str, str):
                        asset = json.loads(asset_str)
                    else:
                        asset = asset_str

                    attachment_info = self.parse_totango_asset(asset)
                    if attachment_info:
                        attachments.append(attachment_info)
                        self.results['attachments_processed'] += 1

                except json.JSONDecodeError as e:
                    logger.error(f"❌ Failed to parse asset JSON: {e}")
                except Exception as e:
                    logger.error(f"❌ Error processing asset: {e}")

            logger.info(f"✅ Extracted {len(attachments)} valid attachments")

        except Exception as e:
            logger.error(f"❌ Error extracting attachments: {e}")

        return attachments

    def parse_totango_asset(self, asset: Dict) -> Optional[Dict]:
        """Parse individual Totango asset into standardized format"""
        try:
            asset_type = asset.get('asset_type', '')
            name = asset.get('name', 'Attachment')
            asset_url = asset.get('asset_url', '')
            download_path = asset.get('downloadPath', '')
            extension = asset.get('extension', '')
            asset_id = asset.get('id', '')

            # Determine the actual URL/path
            if asset_type == 'link' and asset_url:
                # External link (Gong, Zoom, Google Docs, etc.)
                return {
                    'type': 'link',
                    'name': name,
                    'url': asset_url,
                    'extension': extension,
                    'totango_id': asset_id,
                    'needs_upload': False
                }
            elif asset_type == 'file' and download_path:
                # File that needs to be downloaded from Totango using token-based auth
                return {
                    'type': 'file',
                    'name': name,
                    'url': '',  # Will be populated by token request
                    'extension': extension,
                    'totango_id': asset_id,
                    'download_path': download_path,
                    'needs_upload': True
                }
            else:
                logger.warning(f"⚠️ Skipping asset with missing URL/path: {name} (type: {asset_type})")
                return None

        except Exception as e:
            logger.error(f"❌ Error parsing asset: {e}")
            return None

    def extract_links_from_content(self, content: str) -> List[Dict]:
        """Extract embedded links from note content"""
        links = []

        if not content:
            return links

        try:
            # Extract standard URLs from text/HTML
            url_pattern = r'https?://[^\s<>"\'()]+[^\s<>"\'().]'
            urls = re.findall(url_pattern, content)

            # Extract URLs from HTML href attributes
            href_pattern = r'href=["\']([^"\']+)["\']'
            href_urls = re.findall(href_pattern, content, re.IGNORECASE)

            # Combine and deduplicate
            all_urls = list(set(urls + href_urls))

            for url in all_urls:
                # Skip base64 data URLs and invalid URLs
                if url.startswith('data:') or url.startswith('cid:'):
                    continue

                links.append({
                    'type': 'embedded_link',
                    'name': self.get_link_display_name(url),
                    'url': url,
                    'needs_upload': False
                })

            if links:
                logger.info(f"🔗 Extracted {len(links)} embedded links from content")

        except Exception as e:
            logger.error(f"❌ Error extracting links from content: {e}")

        return links

    def get_link_display_name(self, url: str) -> str:
        """Generate a display name for a URL"""
        try:
            parsed = urlparse(url)
            domain = parsed.netloc.lower()

            # Common platform mappings
            if 'gong.io' in domain:
                return 'Gong Recording'
            elif 'zoom.us' in domain:
                return 'Zoom Recording'
            elif 'docs.google.com' in domain:
                return 'Google Document'
            elif 'drive.google.com' in domain:
                return 'Google Drive File'
            elif 'github.com' in domain:
                return 'GitHub Link'
            elif 'confluence' in domain:
                return 'Confluence Page'
            else:
                return f"Link: {domain}"

        except Exception:
            return "Web Link"

    def get_jwt_token_from_config(self) -> Optional[str]:
        """Extract JWT token from config or headers - FIXED VERSION"""
        try:
            # Priority 1: Manual token in config
            manual_token = self.config.get('totango', {}).get('jwt_token', '')
            if manual_token and len(manual_token) > 50:
                logger.info("🔑 Using manually configured JWT token")
                return manual_token

            # Priority 2: Extract from headers
            headers = self.config.get('totango', {}).get('headers', {})

            # Check Authorization header
            auth_header = headers.get('Authorization', '')
            if 'Bearer ' in auth_header:
                token = auth_header.replace('Bearer ', '')
                if self.is_valid_jwt(token):
                    logger.info("🔑 Found valid JWT in Authorization header")
                    return token

            # Check cookies for JWT patterns
            cookie_header = headers.get('Cookie', '')
            if cookie_header:
                jwt_pattern = r'[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+'
                matches = re.findall(jwt_pattern, cookie_header)

                for match in matches:
                    if len(match) > 100 and self.is_valid_jwt(match):
                        logger.info("🔑 Found valid JWT in cookies")
                        return match

            logger.error("❌ No valid JWT token found in config or headers")
            return None

        except Exception as e:
            logger.error(f"❌ Error extracting JWT token: {e}")
            return None

    def is_valid_jwt(self, token: str) -> bool:
        """Check if a string is a valid JWT token"""
        if not token or len(token) < 50:
            return False

        parts = token.split('.')
        if len(parts) != 3:
            return False

        try:
            header = parts[0]
            header += '=' * (4 - len(header) % 4)
            decoded_header = base64.b64decode(header, validate=True)
            header_json = json.loads(decoded_header)
            return 'alg' in header_json and 'typ' in header_json
        except:
            return False

    def download_totango_file(self, attachment: Dict) -> Optional[bytes]:
        """Download file from Totango using JWT token with assets-proxy URL"""
        try:
            asset_id = attachment.get('totango_id', '')
            download_path = attachment.get('download_path', '')

            logger.info(f"📥 Downloading: {attachment['name']} (ID: {asset_id})")

            # Step 1: Get JWT token
            token = self.get_jwt_token_from_config()

            if not token:
                logger.error(f"❌ Could not obtain JWT token for {attachment['name']}")
                return None

            # Step 2: Generate assets-proxy URL with token
            path_parts = download_path.strip('/').split('/')
            if len(path_parts) < 2:
                logger.error(f"❌ Invalid download path format: {download_path}")
                return None

            service_id = path_parts[0]
            file_path = '/'.join(path_parts[1:])

            download_url = f"https://assets-proxy.totango.com/api/v2/assets/{service_id}/{file_path}?token={token}"

            logger.info(f"🔗 Using token URL: {download_url[:100]}...")

            # Step 3: Download using the token URL
            response = self.session.get(
                download_url,
                timeout=60,
                stream=True  # Use streaming for large files
            )

            if response.status_code == 200:
                file_content = response.content
                logger.info(f"✅ Successfully downloaded {len(file_content)} bytes")
                return file_content
            elif response.status_code == 401:
                logger.error(f"❌ Token authentication failed (401) - Token may be expired")
                return None
            elif response.status_code == 403:
                logger.error(f"❌ Access forbidden (403) - Insufficient permissions")
                return None
            elif response.status_code == 404:
                logger.error(f"❌ File not found (404) - File may have been deleted")
                return None
            else:
                logger.error(f"❌ Download failed: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"❌ Error downloading file: {e}")
            return None

    def get_mime_type(self, extension: str) -> str:
        """Get MIME type for file extension"""
        extension = extension.lower().lstrip('.')

        mime_types = {
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt': 'application/vnd.ms-powerpoint',
            'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'gif': 'image/gif',
            'bmp': 'image/bmp',
            'txt': 'text/plain',
            'csv': 'text/csv',
            'json': 'application/json',
            'zip': 'application/zip',
            'rar': 'application/x-rar-compressed'
        }

        return mime_types.get(extension, 'application/octet-stream')

    def upload_to_gainsight(self, attachment: Dict, file_content: bytes = None, activity_id: str = None) -> Optional[str]:
        """Upload attachment to Gainsight using the FIXED format"""
        try:
            if attachment['type'] == 'link' or not attachment.get('needs_upload', False):
                # For links, we'll add them directly to the activity
                logger.info(f"🔗 Link attachment: {attachment['name']} -> {attachment['url']}")
                return attachment['url']

            if not file_content:
                logger.error(f"❌ No file content for upload: {attachment['name']}")
                return None

            logger.info(f"📤 Uploading to Gainsight: {attachment['name']}")
            logger.info(f"  File size: {len(file_content)} bytes")
            logger.info(f"  File type: {self.get_mime_type(attachment['extension'])}")

            # Prepare the multipart form data
            files = {
                'file': (
                    attachment['name'],
                    file_content,
                    self.get_mime_type(attachment['extension'])
                )
            }

            # FIXED: Use the correct form data structure
            form_data = {
                'contexts': json.dumps([{
                    'id': self.config['gainsight']['company_id'],
                    'base': True,
                    'obj': 'Company',
                    'lbl': self.config['gainsight']['company_name'],
                    'eid': None,
                    'eobj': 'Account',
                    'eurl': None,
                    'esys': 'SALESFORCE',
                    'dsp': True
                }]),
                'source': 'C360'
            }

            # Add activity ID if provided
            if activity_id:
                form_data['entityId'] = activity_id

            # Use the correct endpoint
            upload_url = f"{self.config['gainsight']['url']}/v1/ant/attachments"

            # Prepare headers (remove content-type to let requests handle multipart)
            upload_headers = {}
            for key, value in self.config['gainsight']['headers'].items():
                if key.lower() not in ['content-type', 'content-length']:
                    upload_headers[key] = value

            logger.info(f"📡 Uploading to: {upload_url}")

            # Make the upload request
            response = self.session.post(
                upload_url,
                files=files,
                data=form_data,
                headers=upload_headers,
                timeout=120
            )

            logger.info(f"📥 Upload response: {response.status_code}")

            if response.status_code == 200:
                try:
                    result = response.json()

                    if result.get('result') and result.get('data'):
                        attachment_data = result['data']
                        attachment_id = attachment_data.get('id')

                        if attachment_id:
                            logger.info(f"✅ Upload successful! Attachment ID: {attachment_id}")
                            self.results['attachments_uploaded'] += 1
                            return attachment_id
                        else:
                            logger.error(f"❌ No attachment ID in response")
                            logger.error(f"Response data: {attachment_data}")
                    else:
                        logger.error(f"❌ Unexpected response structure: {result}")

                except json.JSONDecodeError as e:
                    logger.error(f"❌ Invalid JSON response: {e}")
                    logger.error(f"Response text: {response.text[:500]}")

            else:
                logger.error(f"❌ Upload failed: {response.status_code}")
                logger.error(f"Response: {response.text[:500]}")

            self.results['attachments_failed'] += 1
            return None

        except Exception as e:
            logger.error(f"❌ Error uploading to Gainsight: {e}")
            self.results['attachments_failed'] += 1
            return None

    def process_all_attachments(self, activity: Dict, activity_id: str = None) -> List[Dict]:
        """Process all attachments for an activity and return Gainsight attachment list"""
        gainsight_attachments = []

        try:
            # 1. Extract attachments from Totango assets
            totango_attachments = self.extract_totango_attachments(activity)

            # 2. Extract embedded links from note content
            note_content = self.extract_content(activity)
            embedded_links = self.extract_links_from_content(note_content)

            # 3. Combine all attachments
            all_attachments = totango_attachments + embedded_links

            if not all_attachments:
                return gainsight_attachments

            logger.info(f"🔗 Processing {len(all_attachments)} total attachments for activity: {activity_id}")

            # 4. Process each attachment
            for attachment in all_attachments:
                try:
                    if attachment['needs_upload']:
                        # Download from Totango and upload to Gainsight with activity ID
                        file_content = self.download_totango_file(attachment)
                        if file_content:
                            attachment_id = self.upload_to_gainsight(attachment, file_content, activity_id)
                            if attachment_id:
                                gainsight_attachments.append({
                                    'id': attachment_id,
                                    'name': attachment['name'],
                                    'seqId': attachment_id,
                                    'published': True,
                                    'removed': False
                                })
                    else:
                        # For links, we'll include them in the note content rather than as attachments
                        # since Gainsight typically handles external links differently
                        logger.info(f"🔗 Keeping link in content: {attachment['name']}")

                except Exception as e:
                    logger.error(f"❌ Error processing attachment {attachment['name']}: {e}")
                    continue

            logger.info(f"✅ Processed {len(gainsight_attachments)} attachments for upload")

        except Exception as e:
            logger.error(f"❌ Error processing attachments: {e}")

        return gainsight_attachments

    # ============================================================================
    # ACTIVITY TRANSFORMATION AND MIGRATION METHODS
    # ============================================================================

    def extract_content(self, activity: Dict) -> str:
        """Extract content from activity properties"""
        try:
            properties = activity.get('properties', {})

            # Try different content fields
            content_fields = ['content', 'note', 'description', 'summary', 'text']

            for field in content_fields:
                if field in properties and properties[field]:
                    content = properties[field]
                    if isinstance(content, str):
                        return content
                    elif isinstance(content, dict):
                        # Sometimes content is nested
                        return content.get('text', '') or content.get('html', '') or str(content)

            return ""

        except Exception as e:
            logger.error(f"❌ Error extracting content: {e}")
            return ""

    def transform_activity(self, activity: Dict, users: Dict, activity_types: Dict,
                          touchpoint_reasons: Dict = None, flow_types: Dict = None) -> Optional[Dict]:
        """Transform Totango activity to Gainsight format with attachments"""
        try:
            # Extract basic activity data
            activity_id = activity.get('id', '')
            account_id = activity.get('sourceAccountId', '')

            # Get user information
            user_email = activity.get('user', {}).get('email', '').lower()
            user_info = users.get(user_email, {})

            if not user_info:
                logger.warning(f"⚠️ User not found: {user_email}")
                # Use a default user or skip
                return None

            # Extract activity properties
            properties = activity.get('properties', {})
            meeting_type = properties.get('meeting_type', 'MEETING').upper()

            # Get activity type ID
            activity_type_id = activity_types.get(meeting_type)
            if not activity_type_id:
                logger.warning(f"⚠️ Activity type not found: {meeting_type}")
                return None

            # Extract content and process attachments
            content = self.extract_content(activity)

            # Process attachments and get Gainsight attachment list
            # Note: We'll process attachments after creating the activity to get the activity ID

            # Build the Gainsight payload
            payload = {
                'activityType': activity_type_id,
                'companyId': self.config['gainsight']['company_id'],
                'userId': user_info['id'],
                'activityDate': self.convert_timestamp(activity.get('eventTime', '')),
                'subject': properties.get('subject', f"{meeting_type} - {account_id}"),
                'content': content,
                'attachments': [],  # Will be populated after upload
                'customFields': self.extract_custom_fields(activity, touchpoint_reasons, flow_types)
            }

            return payload

        except Exception as e:
            logger.error(f"❌ Error transforming activity: {e}")
            return None

    def convert_timestamp(self, timestamp_str: str) -> str:
        """Convert timestamp to Gainsight format"""
        try:
            if not timestamp_str:
                return datetime.now().isoformat()

            # Handle different timestamp formats
            if isinstance(timestamp_str, (int, float)):
                dt = datetime.fromtimestamp(timestamp_str / 1000)  # Assuming milliseconds
            else:
                # Try parsing as ISO format
                dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))

            return dt.isoformat()

        except Exception as e:
            logger.warning(f"⚠️ Error converting timestamp {timestamp_str}: {e}")
            return datetime.now().isoformat()

    def extract_custom_fields(self, activity: Dict, touchpoint_reasons: Dict = None,
                             flow_types: Dict = None) -> Dict:
        """Extract and map custom fields"""
        custom_fields = {}

        try:
            properties = activity.get('properties', {})

            # Map touchpoint reason
            touchpoint_reason_id = properties.get('touchpoint_reason_id', '')
            if touchpoint_reason_id and touchpoint_reason_id in self.touchpoint_id_mapping:
                mapped_id = self.touchpoint_id_mapping[touchpoint_reason_id]
                custom_fields['touchpointReason'] = mapped_id
                self.results['mappings_applied']['touchpoint_reason_mappings'] += 1
                logger.info(f"🎯 Mapped touchpoint reason: {touchpoint_reason_id} -> {mapped_id}")

            # Map flow type
            flow_type = properties.get('flow_type', '')
            if flow_type and flow_type in self.flow_type_id_mapping:
                mapped_id = self.flow_type_id_mapping[flow_type]
                custom_fields['flowType'] = mapped_id
                self.results['mappings_applied']['flow_type_mappings'] += 1
                logger.info(f"🎯 Mapped flow type: {flow_type} -> {mapped_id}")

            # Add other custom fields as needed
            if 'duration' in properties:
                custom_fields['duration'] = properties['duration']

            if 'outcome' in properties:
                custom_fields['outcome'] = properties['outcome']

        except Exception as e:
            logger.error(f"❌ Error extracting custom fields: {e}")

        return custom_fields

    def create_draft(self, payload: Dict) -> Optional[str]:
        """Create draft activity in Gainsight"""
        try:
            url = f"{self.config['gainsight']['url']}/v1/ant/drafts"

            response = self.session.post(
                url,
                json=payload,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                draft_id = result.get('data', {}).get('id')
                if draft_id:
                    logger.info(f"✅ Created draft: {draft_id}")
                    return draft_id
                else:
                    logger.error(f"❌ No draft ID in response: {result}")
            else:
                logger.error(f"❌ Draft creation failed: {response.status_code}")
                logger.error(f"Response: {response.text[:500]}")

        except Exception as e:
            logger.error(f"❌ Error creating draft: {e}")

        return None

    def create_timeline_entry(self, payload: Dict, draft_id: str) -> bool:
        """Create timeline entry from draft"""
        try:
            url = f"{self.config['gainsight']['url']}/v1/ant/timeline"

            timeline_payload = {
                'draftId': draft_id,
                'companyId': payload['companyId']
            }

            response = self.session.post(
                url,
                json=timeline_payload,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                timeline_id = result.get('data', {}).get('id')
                if timeline_id:
                    logger.info(f"✅ Created timeline entry: {timeline_id}")
                    return True
                else:
                    logger.error(f"❌ No timeline ID in response: {result}")
            else:
                logger.error(f"❌ Timeline creation failed: {response.status_code}")
                logger.error(f"Response: {response.text[:500]}")

        except Exception as e:
            logger.error(f"❌ Error creating timeline entry: {e}")

        return False

    def migrate_single_activity_with_attachments(self, activity: Dict, users: Dict,
                                               activity_types: Dict, touchpoint_reasons: Dict = None,
                                               flow_types: Dict = None) -> Dict:
        """Migrate a single activity with full attachment support"""
        result = {
            'success': False,
            'activity_id': activity.get('id', 'unknown'),
            'account_id': activity.get('sourceAccountId', 'unknown'),
            'error': None,
            'attachments_processed': 0,
            'attachments_uploaded': 0
        }

        try:
            # Step 1: Transform activity to Gainsight format
            payload = self.transform_activity(activity, users, activity_types, touchpoint_reasons, flow_types)
            if not payload:
                result['error'] = 'Transform failed'
                return result

            # Step 2: Create draft activity
            draft_id = self.create_draft(payload)
            if not draft_id:
                result['error'] = 'Draft creation failed'
                return result

            # Step 3: Create timeline entry
            timeline_success = self.create_timeline_entry(payload, draft_id)
            if not timeline_success:
                result['error'] = 'Timeline creation failed'
                return result

            # Step 4: Process attachments (now that we have the activity created)
            # Note: In a real implementation, you'd need to get the actual activity ID from the timeline response
            gainsight_attachments = self.process_all_attachments(activity, draft_id)

            # Update results
            result['success'] = True
            result['attachments_processed'] = len(self.extract_totango_attachments(activity))
            result['attachments_uploaded'] = len(gainsight_attachments)

            logger.info(f"✅ Successfully migrated activity {result['activity_id']} with {len(gainsight_attachments)} attachments")

        except Exception as e:
            result['error'] = str(e)
            logger.error(f"❌ Error migrating activity {result['activity_id']}: {e}")

        return result

    def migrate_activities(self, account_ids: List[str], limit: int = None) -> Dict:
        """Main migration method with full attachment support"""
        logger.info(f"🚀 Starting migration for {len(account_ids)} accounts with attachment support")

        try:
            # Load required data
            users = self.get_gainsight_users()
            activity_types = self.get_gainsight_activity_types(self.config['gainsight']['company_id'])

            if not users:
                logger.error("❌ No users loaded - cannot proceed")
                return self.results

            if not activity_types:
                logger.error("❌ No activity types loaded - cannot proceed")
                return self.results

            # Get activities from Totango
            activities = self.get_totango_activities(account_ids)

            if limit:
                activities = activities[:limit]
                logger.info(f"🔢 Limited to {limit} activities for testing")

            logger.info(f"📋 Processing {len(activities)} activities...")

            # Process each activity
            for i, activity in enumerate(activities, 1):
                logger.info(f"🔄 Processing activity {i}/{len(activities)}: {activity.get('id', 'unknown')}")

                result = self.migrate_single_activity_with_attachments(
                    activity, users, activity_types
                )

                if result['success']:
                    self.results['success'] += 1
                else:
                    self.results['failed'] += 1
                    self.results['errors'].append({
                        'activity_id': result['activity_id'],
                        'error': result['error']
                    })

                # Add small delay to avoid rate limiting
                time.sleep(0.5)

            # Final summary
            logger.info(f"🎉 Migration completed!")
            logger.info(f"✅ Successful: {self.results['success']}")
            logger.info(f"❌ Failed: {self.results['failed']}")
            logger.info(f"📎 Attachments processed: {self.results['attachments_processed']}")
            logger.info(f"📤 Attachments uploaded: {self.results['attachments_uploaded']}")
            logger.info(f"❌ Attachments failed: {self.results['attachments_failed']}")

        except Exception as e:
            logger.error(f"❌ Migration failed: {e}")

        return self.results


# Example usage and testing
if __name__ == "__main__":
    # Example configuration
    config = {
        'totango': {
            'url': 'https://app.totango.com',
            'headers': {
                'Cookie': 'your_totango_session_cookies_here'
            },
            'jwt_token': 'YOUR_JWT_TOKEN_HERE'  # Add your actual JWT token
        },
        'gainsight': {
            'url': 'https://demo-emea1.gainsightcloud.com',
            'company_id': 'YOUR_COMPANY_ID',
            'company_name': 'Your Company Name',
            'headers': {
                'Cookie': 'your_gainsight_session_cookies_here'
            }
        }
    }

    # Test account IDs
    test_account_ids = ['001b000003nwKk1AAE']  # Replace with your test account IDs

    print("🧪 COMPLETE WORKING MIGRATOR WITH ATTACHMENTS")
    print("=" * 60)
    print("⚠️  IMPORTANT: Update the config with your actual values before running!")
    print("=" * 60)

    # Uncomment to run migration (after updating config)
    # migrator = CompleteTotangoGainsightMigrator()
    # migrator.config = config
    # results = migrator.migrate_activities(test_account_ids, limit=5)  # Test with 5 activities first
    # print(f"\n📊 Final Results: {results}")
