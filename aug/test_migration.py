#!/usr/bin/env python3
"""
Test script for the complete working migrator with attachments
"""

import json
import sys
import os
from complete_working_migrator import CompleteTotangoGainsightMigrator

def load_config(config_file="config.json"):
    """Load configuration from file"""
    if not os.path.exists(config_file):
        print(f"❌ Config file not found: {config_file}")
        print("📋 Please copy config_template.json to config.json and update with your values")
        return None
    
    try:
        with open(config_file, 'r') as f:
            return json.load(f)
    except Exception as e:
        print(f"❌ Error loading config: {e}")
        return None

def test_jwt_extraction(migrator):
    """Test JWT token extraction"""
    print("\n🔑 Testing JWT Token Extraction...")
    print("-" * 40)
    
    token = migrator.get_jwt_token_from_config()
    if token:
        print(f"✅ JWT Token found: {token[:50]}...")
        print(f"   Token length: {len(token)}")
        print(f"   Valid JWT: {migrator.is_valid_jwt(token)}")
        return True
    else:
        print("❌ No JWT token found")
        print("💡 Make sure to add your JWT token to the config file")
        return False

def test_gainsight_connection(migrator):
    """Test Gainsight connection"""
    print("\n🔗 Testing Gainsight Connection...")
    print("-" * 40)
    
    try:
        users = migrator.get_gainsight_users()
        if users:
            print(f"✅ Connected to Gainsight - Found {len(users)} users")
            return True
        else:
            print("❌ No users found - check Gainsight connection")
            return False
    except Exception as e:
        print(f"❌ Gainsight connection failed: {e}")
        return False

def test_totango_connection(migrator, test_account_id):
    """Test Totango connection"""
    print("\n📊 Testing Totango Connection...")
    print("-" * 40)
    
    try:
        activities = migrator.get_totango_activities([test_account_id])
        if activities:
            print(f"✅ Connected to Totango - Found {len(activities)} activities")
            
            # Show first activity with attachments
            for activity in activities[:1]:
                attachments = migrator.extract_totango_attachments(activity)
                print(f"   Activity {activity.get('id', 'unknown')}: {len(attachments)} attachments")
                for att in attachments[:3]:  # Show first 3 attachments
                    print(f"     - {att['name']} ({att['type']})")
            
            return True
        else:
            print("❌ No activities found - check account ID")
            return False
    except Exception as e:
        print(f"❌ Totango connection failed: {e}")
        return False

def test_attachment_download(migrator, test_account_id):
    """Test attachment download"""
    print("\n📥 Testing Attachment Download...")
    print("-" * 40)
    
    try:
        activities = migrator.get_totango_activities([test_account_id])
        
        for activity in activities:
            attachments = migrator.extract_totango_attachments(activity)
            
            for attachment in attachments:
                if attachment['needs_upload']:
                    print(f"🔄 Testing download: {attachment['name']}")
                    
                    file_content = migrator.download_totango_file(attachment)
                    if file_content:
                        print(f"✅ Downloaded {len(file_content)} bytes")
                        return True
                    else:
                        print(f"❌ Download failed")
        
        print("⚠️ No downloadable attachments found")
        return False
        
    except Exception as e:
        print(f"❌ Attachment download test failed: {e}")
        return False

def run_test_migration(migrator, test_account_ids, limit=2):
    """Run a small test migration"""
    print(f"\n🚀 Running Test Migration ({limit} activities)...")
    print("-" * 50)
    
    try:
        results = migrator.migrate_activities(test_account_ids, limit=limit)
        
        print(f"\n📊 Test Results:")
        print(f"✅ Successful: {results['success']}")
        print(f"❌ Failed: {results['failed']}")
        print(f"📎 Attachments processed: {results['attachments_processed']}")
        print(f"📤 Attachments uploaded: {results['attachments_uploaded']}")
        print(f"❌ Attachments failed: {results['attachments_failed']}")
        
        if results['errors']:
            print(f"\n❌ Errors:")
            for error in results['errors'][:3]:  # Show first 3 errors
                print(f"   - {error['activity_id']}: {error['error']}")
        
        return results['success'] > 0
        
    except Exception as e:
        print(f"❌ Test migration failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 TESTING COMPLETE WORKING MIGRATOR WITH ATTACHMENTS")
    print("=" * 60)
    
    # Load configuration
    config = load_config()
    if not config:
        return
    
    # Initialize migrator
    migrator = CompleteTotangoGainsightMigrator()
    migrator.config = config
    
    # Test account ID (replace with your test account)
    test_account_id = "001b000003nwKk1AAE"  # Update this
    
    print(f"🎯 Testing with account ID: {test_account_id}")
    
    # Run tests
    tests_passed = 0
    total_tests = 5
    
    # Test 1: JWT Token
    if test_jwt_extraction(migrator):
        tests_passed += 1
    
    # Test 2: Gainsight Connection
    if test_gainsight_connection(migrator):
        tests_passed += 1
    
    # Test 3: Totango Connection
    if test_totango_connection(migrator, test_account_id):
        tests_passed += 1
    
    # Test 4: Attachment Download
    if test_attachment_download(migrator, test_account_id):
        tests_passed += 1
    
    # Test 5: Small Migration
    if run_test_migration(migrator, [test_account_id], limit=2):
        tests_passed += 1
    
    # Final summary
    print(f"\n🎉 TEST SUMMARY")
    print("=" * 30)
    print(f"✅ Tests passed: {tests_passed}/{total_tests}")
    
    if tests_passed == total_tests:
        print("🎊 All tests passed! Your migrator is ready for production.")
    elif tests_passed >= 3:
        print("⚠️ Most tests passed. Check the failed tests and fix issues.")
    else:
        print("❌ Multiple tests failed. Please check your configuration and connections.")
    
    print("\n💡 Next steps:")
    print("1. Fix any failed tests")
    print("2. Update account IDs for your migration")
    print("3. Run full migration with: python complete_working_migrator.py")

if __name__ == "__main__":
    main()
