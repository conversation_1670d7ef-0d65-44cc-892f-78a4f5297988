#!/usr/bin/env python3
"""
Run migration script for <PERSON>'s Totango to Gainsight migration
"""

import sys
import os
import json
from complete_working_migrator import CompleteTotangoGainsightMigrator

def main():
    print("🚀 STARTING TOTANGO TO GAINSIGHT MIGRATION")
    print("=" * 60)
    print("Company: ICICI")
    print("User: <PERSON> (<EMAIL>)")
    print("=" * 60)
    
    # Initialize migrator with your config
    try:
        migrator = CompleteTotangoGainsightMigrator("config.json")
        print("✅ Configuration loaded successfully")
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return
    
    # Test account IDs - UPDATE THESE WITH YOUR ACTUAL ACCOUNT IDs
    test_account_ids = [
        "001b000003nwKk1AAE",  # Replace with your actual account IDs
        # Add more account IDs here as needed
    ]
    
    print(f"\n🎯 Target Accounts: {len(test_account_ids)}")
    for i, account_id in enumerate(test_account_ids, 1):
        print(f"   {i}. {account_id}")
    
    # Step 1: Test JWT Token
    print(f"\n🔑 Step 1: Testing JWT Token...")
    token = migrator.get_jwt_token_from_config()
    if token:
        print(f"✅ JWT Token found: {token[:50]}...")
        print(f"   Token length: {len(token)}")
        print(f"   Valid JWT: {migrator.is_valid_jwt(token)}")
    else:
        print("❌ No JWT token found - migration cannot proceed")
        return
    
    # Step 2: Test Gainsight Connection
    print(f"\n🔗 Step 2: Testing Gainsight Connection...")
    try:
        users = migrator.get_gainsight_users()
        if users:
            print(f"✅ Connected to Gainsight - Found {len(users)} users")
            
            # Check if your user exists
            your_email = "<EMAIL>"
            if your_email.lower() in users:
                user_info = users[your_email.lower()]
                print(f"✅ Your user found: {user_info['name']} (ID: {user_info['id']})")
            else:
                print(f"⚠️ Your email {your_email} not found in Gainsight users")
        else:
            print("❌ No users found - check Gainsight connection")
            return
    except Exception as e:
        print(f"❌ Gainsight connection failed: {e}")
        return
    
    # Step 3: Test Totango Connection
    print(f"\n📊 Step 3: Testing Totango Connection...")
    try:
        activities = migrator.get_totango_activities(test_account_ids[:1])  # Test with first account
        if activities:
            print(f"✅ Connected to Totango - Found {len(activities)} activities")
            
            # Show sample activity with attachments
            for activity in activities[:3]:  # Show first 3 activities
                attachments = migrator.extract_totango_attachments(activity)
                activity_id = activity.get('id', 'unknown')
                print(f"   Activity {activity_id}: {len(attachments)} attachments")
                
                for att in attachments[:2]:  # Show first 2 attachments
                    print(f"     - {att['name']} ({att['type']})")
        else:
            print("❌ No activities found - check account IDs")
            return
    except Exception as e:
        print(f"❌ Totango connection failed: {e}")
        return
    
    # Step 4: Ask user for confirmation
    print(f"\n🤔 Ready to proceed with migration?")
    print(f"   - {len(test_account_ids)} accounts")
    print(f"   - {len(activities)} activities found")
    print(f"   - Attachments will be migrated")
    
    response = input("\nProceed? (y/N): ").strip().lower()
    if response != 'y':
        print("❌ Migration cancelled by user")
        return
    
    # Step 5: Run Small Test First
    print(f"\n🧪 Step 5: Running Small Test (5 activities)...")
    try:
        test_results = migrator.migrate_activities(test_account_ids, limit=5)
        
        print(f"\n📊 Test Results:")
        print(f"✅ Successful: {test_results['success']}")
        print(f"❌ Failed: {test_results['failed']}")
        print(f"📎 Attachments processed: {test_results['attachments_processed']}")
        print(f"📤 Attachments uploaded: {test_results['attachments_uploaded']}")
        print(f"❌ Attachments failed: {test_results['attachments_failed']}")
        
        if test_results['errors']:
            print(f"\n❌ Errors in test:")
            for error in test_results['errors'][:3]:
                print(f"   - {error['activity_id']}: {error['error']}")
        
        if test_results['success'] == 0:
            print("❌ Test failed - stopping migration")
            return
            
    except Exception as e:
        print(f"❌ Test migration failed: {e}")
        return
    
    # Step 6: Ask for full migration
    print(f"\n🎉 Test successful! Proceed with full migration?")
    response = input("Run full migration? (y/N): ").strip().lower()
    if response != 'y':
        print("✅ Migration completed with test only")
        return
    
    # Step 7: Full Migration
    print(f"\n🚀 Step 7: Running Full Migration...")
    try:
        final_results = migrator.migrate_activities(test_account_ids)
        
        print(f"\n🎊 FINAL MIGRATION RESULTS")
        print("=" * 40)
        print(f"✅ Successful activities: {final_results['success']}")
        print(f"❌ Failed activities: {final_results['failed']}")
        print(f"📎 Attachments processed: {final_results['attachments_processed']}")
        print(f"📤 Attachments uploaded: {final_results['attachments_uploaded']}")
        print(f"❌ Attachments failed: {final_results['attachments_failed']}")
        
        # Save results to file
        with open('migration_results.json', 'w') as f:
            json.dump(final_results, f, indent=2)
        print(f"\n💾 Results saved to migration_results.json")
        
        if final_results['errors']:
            print(f"\n❌ Errors encountered:")
            for error in final_results['errors'][:5]:
                print(f"   - {error['activity_id']}: {error['error']}")
            
            if len(final_results['errors']) > 5:
                print(f"   ... and {len(final_results['errors']) - 5} more errors")
        
        success_rate = (final_results['success'] / (final_results['success'] + final_results['failed'])) * 100 if (final_results['success'] + final_results['failed']) > 0 else 0
        print(f"\n📈 Success Rate: {success_rate:.1f}%")
        
        if success_rate > 90:
            print("🎊 Excellent migration results!")
        elif success_rate > 75:
            print("✅ Good migration results!")
        else:
            print("⚠️ Some issues encountered - review errors")
            
    except Exception as e:
        print(f"❌ Full migration failed: {e}")
        return
    
    print(f"\n🎉 Migration completed! Check Gainsight for migrated activities.")

if __name__ == "__main__":
    main()
