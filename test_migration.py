#!/usr/bin/env python3
"""
🧪 Enhanced Totango to Gainsight Migration - Test Runner
========================================================
Test the migration with a small batch before running full migration
"""

import os
import sys
import json
from pathlib import Path

# Add the current directory to path to import the migrator
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def run_test_migration():
    """Run a test migration with a small batch"""
    print("🧪 Enhanced Totango to Gainsight Migration - Test Run")
    print("="*60)
    
    # Check if main migrator exists
    migrator_file = current_dir / "enhanced_totango_gainsight_migrator_complete.py"
    if not migrator_file.exists():
        print("❌ Main migrator file not found!")
        print(f"Expected: {migrator_file}")
        return False
    
    # Check if config exists
    config_file = current_dir / "migration_config.json"
    if not config_file.exists():
        print("❌ Configuration file not found!")
        print(f"Expected: {config_file}")
        print("💡 Run setup.py first to create configuration")
        return False
    
    try:
        # Import the migrator
        from enhanced_totango_gainsight_migrator_complete import EnhancedTotangoGainsightMigrator
        
        # Test account IDs (replace with actual test accounts)
        test_account_ids = [
            'test_account_1',  # Replace with a real account ID for testing
            'test_account_2'   # Replace with a real account ID for testing
        ]
        
        print("🎯 Test Configuration:")
        print(f"   📁 Config file: {config_file}")
        print(f"   🏢 Test accounts: {len(test_account_ids)}")
        print(f"   📝 Accounts: {', '.join(test_account_ids)}")
        
        # Load and display config summary
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        print(f"   🌐 Totango URL: {config['totango']['url']}")
        print(f"   🎯 Gainsight URL: {config['gainsight']['url']}")
        print(f"   👤 Gainsight User: {config['gainsight']['user_name']}")
        print(f"   🏢 Target Company: {config['gainsight']['company_name']}")
        
        # Prompt user to confirm
        print("\n⚠️ IMPORTANT: This is a test run with limited accounts.")
        print("Make sure your test account IDs are valid before proceeding.")
        
        user_input = input("\nProceed with test migration? (y/N): ").strip().lower()
        if user_input not in ['y', 'yes']:
            print("🛑 Test cancelled by user")
            return False
        
        print("\n🚀 Starting test migration...")
        
        # Initialize migrator with test-specific settings
        migrator = EnhancedTotangoGainsightMigrator(str(config_file))
        
        # Override settings for testing
        migrator.config['migration']['batch_size'] = 2  # Smaller batches for testing
        migrator.config['migration']['parallel_workers'] = 1  # Sequential for easier debugging
        migrator.config['migration']['rate_limit_delay'] = 1.0  # More conservative rate limiting
        
        print("🔧 Test settings applied:")
        print(f"   📦 Batch size: {migrator.config['migration']['batch_size']}")
        print(f"   🔄 Parallel workers: {migrator.config['migration']['parallel_workers']}")
        print(f"   ⏱️ Rate limit delay: {migrator.config['migration']['rate_limit_delay']}s")
        
        # Run migration
        migrator.migrate_all_activities(test_account_ids)
        
        print("\n🎉 Test migration completed!")
        print("📊 Check the generated report and logs for results")
        print("📁 Output files saved in: /Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import migrator: {e}")
        print("💡 Make sure the migrator file is in the same directory")
        return False
    except Exception as e:
        print(f"❌ Test migration failed: {e}")
        return False

def validate_test_accounts():
    """Help user validate their test account IDs"""
    print("🔍 Test Account Validation Helper")
    print("="*40)
    
    print("To get valid Totango account IDs for testing:")
    print("1. Log into Totango")
    print("2. Navigate to Accounts page")
    print("3. Select a few test accounts")
    print("4. Note their account IDs from the URL or account details")
    print("5. Use these IDs in the test_account_ids list")
    
    print("\nExample account ID formats:")
    print("  • account_12345")
    print("  • acct_abc123")
    print("  • company_xyz789")
    
    print("\n💡 Start with 1-2 accounts to test the integration")

def show_test_help():
    """Show help for running tests"""
    print("🧪 TEST RUNNER HELP")
    print("="*20)
    
    print("\nAvailable commands:")
    print("  python test_migration.py              - Run test migration")
    print("  python test_migration.py --validate   - Show account validation help")
    print("  python test_migration.py --help       - Show this help")
    
    print("\nBefore running tests:")
    print("1. Complete setup: python setup.py")
    print("2. Update configuration with real credentials")
    print("3. Edit test account IDs in this file")
    print("4. Run test migration")
    
    print("\nAfter successful test:")
    print("1. Review test results and logs")
    print("2. Update main script with full account list")
    print("3. Run full migration")

def main():
    """Main function"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "--validate":
            validate_test_accounts()
            return
        elif sys.argv[1] == "--help":
            show_test_help()
            return
    
    # Run test migration
    success = run_test_migration()
    
    if success:
        print("\n✅ Test completed successfully!")
        print("💡 If results look good, proceed with full migration")
    else:
        print("\n❌ Test failed!")
        print("💡 Check the error messages and fix issues before proceeding")

if __name__ == "__main__":
    main()
