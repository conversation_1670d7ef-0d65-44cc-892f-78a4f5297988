#!/usr/bin/env python3
"""
JWT Token Configuration Helper
Helps you add a JWT token to your migration config and test it
"""

import json
import requests
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("TokenHelper")

def add_token_to_config(token: str, config_file: str):
    """Add JWT token to migration config"""
    try:
        # Load existing config
        with open(config_file, 'r') as f:
            config = json.load(f)
        
        # Add the token
        if 'totango' not in config:
            config['totango'] = {}
        
        config['totango']['jwt_token'] = token
        
        # Save updated config
        with open(config_file, 'w') as f:
            json.dump(config, f, indent=2)
        
        logger.info(f"✅ Token added to {config_file}")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error updating config: {e}")
        return False

def test_token(token: str):
    """Test if the JWT token works for downloading"""
    try:
        # Test with the known working file from your data
        test_url = f"https://assets-proxy.totango.com/api/v2/assets/45606/account/001b000003nwKk1AAE/uploads/GYh4AYcBQNQJt_S2xPdz?token={token}"
        
        logger.info("🧪 Testing JWT token...")
        logger.info(f"Test URL: {test_url[:80]}...")
        
        response = requests.head(test_url, timeout=30)
        
        if response.status_code == 200:
            size = response.headers.get('content-length', 'unknown')
            logger.info(f"✅ TOKEN WORKS! File size: {size} bytes")
            return True
        elif response.status_code == 401:
            logger.error(f"❌ Token is invalid or expired (401)")
            return False
        elif response.status_code == 403:
            logger.error(f"❌ Token doesn't have permission (403)")
            return False
        else:
            logger.error(f"❌ Test failed: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Token test error: {e}")
        return False

def main():
    print("🔑 JWT TOKEN CONFIGURATION HELPER")
    print("="*45)
    print()
    
    # Instructions
    print("📋 TO GET YOUR JWT TOKEN:")
    print("1. Open Totango in your browser")
    print("2. Go to any page with file attachments")
    print("3. Open Developer Tools (F12)")
    print("4. Go to Network tab")
    print("5. Try to download any attachment")
    print("6. Look for requests to 'assets-proxy.totango.com'")
    print("7. Copy the 'token' parameter from the URL")
    print()
    
    # Get token from user
    print("🔑 Enter your JWT token (paste it here):")
    token = input().strip()
    
    if not token:
        print("❌ No token provided")
        return
    
    if len(token) < 50:
        print("❌ Token seems too short - make sure you copied the full token")
        return
    
    # Test the token
    print("\n🧪 Testing your token...")
    if test_token(token):
        print("\n✅ TOKEN WORKS!")
        
        # Add to config
        config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
        
        if add_token_to_config(token, config_file):
            print(f"✅ Token saved to migration_config.json")
            print("\n🚀 You can now run the enhanced migrator:")
            print("python3 enhanced_attachment_migrator.py")
        else:
            print("❌ Failed to save token to config")
    else:
        print("\n❌ TOKEN DOESN'T WORK")
        print("Please check:")
        print("- Make sure you copied the complete token")
        print("- The token might have expired - try getting a fresh one")
        print("- Make sure you're logged into Totango")

if __name__ == "__main__":
    main()
