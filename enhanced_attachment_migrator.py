#!/usr/bin/env python3
"""
ENHANCED Totango to Gainsight Migrator with ATTACHMENT SUPPORT
This version includes comprehensive attachment handling for both links and files
"""

import requests
import json
import time
import logging
import os
import re
import base64
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from collections import defaultdict
from urllib.parse import urlparse, unquote

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("EnhancedTotangoMigrator")

class EnhancedTotangoGainsightMigrator:
    
    def __init__(self, config_file=None):
        self.config = self.load_config(config_file)
        self.session = requests.Session()
        self.results = {
            'success': 0,
            'failed': 0,
            'errors': [],
            'activity_types': {},
            'attachments_processed': 0,
            'attachments_uploaded': 0,
            'attachments_failed': 0,
            'mappings_applied': {
                'touchpoint_reason_mappings': 0,
                'flow_type_mappings': 0
            }
        }
        
        # Direct ID mappings from your paste.txt document
        self.touchpoint_id_mapping = {
            "12980f6d-797c-47da-ab58-78f3a3e5c954": "1I00J1INQCQ6GQ3T2MID7L1S4X8HPJ2OP95B",  # SCANNER MAINTENANCE
            "16a15fcd-d735-49a7-8b14-ca279e6dba5a": "1I00J1INQCQ6GQ3T2MOPQWUV1NFURNPEK8VE",  # MEND CONTAINER
            "30989b43-ff62-486c-b4db-f89c3106abba": "1I00J1INQCQ6GQ3T2MWULITYE9ASDFPQ2KD3",  # ACCOUNT REVIEW
            "38bb62d0-4b50-4a5f-906c-6edbfe4786db": "1I00J1INQCQ6GQ3T2MYNG8BC6SJCCN7SAPKC",  # MEND SCA
            "38ce7c4e-374f-42db-94e8-23ba48d4b1e3": "1I00J1INQCQ6GQ3T2MTA6WH5AKWS5JL0O87R",  # ESCALATION
            "6cc323e4-4155-48bf-964c-ec75ee70e96b": "1I00J1INQCQ6GQ3T2MGRUSTP2XZGZBXN5263",  # MEND PLATFORM ACCESS
            "7c285778-76be-4543-b8a1-9066ee5a1ad4": "1I00J1INQCQ6GQ3T2M0O5F16H63MEMNDN97J",  # MEND SAST
            "9165fe90-5bc2-480d-98b2-76f3926c8e23": "1I00J1INQCQ6GQ3T2MY27A9SQHEE253247F2",  # SUPPORT
            "a2d4c216-8d7b-4f24-9cdf-3c4bc0226f94": "1I00J1INQCQ6GQ3T2MJY0GQI8EZ9STP11VR2",  # MARKETING
            "b1120b9a-85e9-4c2c-b7e7-9214ac3baaea": "1I00J1INQCQ6GQ3T2M3D94LFFY2R8UMNJXE0",  # NPS
            "c5d4cd04-1224-4ad3-8696-d2fb70ea85be": "1I00J1INQCQ6GQ3T2MXT1PJXBF3KATW6QR4K",  # PRODUCT MANAGEMENT ENGAGEMENT
            "cbd1f1f4-229a-4e1b-b4a3-a091e90932cc": "1I00J1INQCQ6GQ3T2M6MLMI12JOYH1Z05ILP",  # ONBOARDING
            "ceb3f746-7099-4083-bc18-7d941699c6b5": "1I00J1INQCQ6GQ3T2M9VQOCBJ5RVU2YW5V4B",  # REACHABILITY (SCA)
            "cefe339e-50d6-434d-a126-d66d652ce06c": "1I00J1INQCQ6GQ3T2MENNOYNKWB0ZEM5WII6",  # MEND AI
            "ddf60bee-a1d2-4b03-b1bd-1f59eef0e214": "1I00J1INQCQ6GQ3T2M2M84A559MT05AWXZT7",  # OUTAGE
            "e43ae486-e0b8-4e2c-a8f3-fe17821b003d": "1I00J1INQCQ6GQ3T2MRYA87NSXMM46FZCUZH",  # CADENCE
            "e4b6a6cf-0833-4a60-aac2-52759e73e9ab": "1I00J1INQCQ6GQ3T2M16QWTIDVSRJTOA8219",  # RENEWAL
            "eb5d8037-0f63-452e-8007-977e91e061b5": "1I00J1INQCQ6GQ3T2MZKWMBM3ZATASUU2VFS",  # REFERENCE
            "ed358b0e-5cad-42e6-80be-04df0db7dc2c": "1I00J1INQCQ6GQ3T2MQN0KLL5SPTQZYCWWG6",  # FEE
            "f9f8cc62-1436-4f96-afe7-ef77a2ff18ed": "1I00J1INQCQ6GQ3T2MNUINIM89ZGC48DOXQR"   # SAST CLOUD MIGRATION
        }
        
        # Direct flow type mappings
        self.flow_type_id_mapping = {
            "renewal": "1I00EBMOY66ROGCBY6844WMEDZF8B8RXHTP1",                    # Renewal
            "support": "1I00EBMOY66ROGCBY66HJSD9I0AG8E8Q1A0D",                    # Support
            "upsell": "1I00EBMOY66ROGCBY6J9O3U5YYU9F5NDTN2X",                     # Upsell
            "escalation": "1I00EBMOY66ROGCBY6L0K6AGIJF5FPS2BATD",                # Escalation
            "onboarding_101": "1I00EBMOY66ROGCBY63I51PAXG5OJRPK37AQ",            # Onboarding
            "adoption": "1I00EBMOY66ROGCBY6IJJ2GVGY81F62M23Z6",                   # Adoption
            "risk_1560979263618": "1I00EBMOY66ROGCBY6YKHPSNQCJ382C3YQQU",        # Risk
            "product_transition_1630456786595": "1I00EBMOY66ROGCBY6F8BZABGHFA7Z1OOYW5",  # Product Transition
            "intelligence_1561140678082": "1I00EBMOY66ROGCBY6H0LHB0SGQLAU3N58O4",     # Intelligence
            "services_1631204797082": "1I00EBMOY66ROGCBY650ON17KW7GYGD2H5ZY",        # Services
            "inbound_1631240727463": "1I00EBMOY66ROGCBY6YAF5US4GK95QEO5ZFU",         # Inbound
            "design_partner_1635451768576": "1I00EBMOY66ROGCBY6F8NQVJY43GPGH1XTD8",   # Design Partner
            "nps_1654*********": "1I00EBMOY66ROGCBY68KLFLZ3KPM5GGP9YUH",            # NPS
            "business_review_1628207371592": "1I00EBMOY66ROGCBY6EOK9JO7C3HUUM9O5F7",  # Business Review
            "journey_1713548309375": "1I00EBMOY66ROGCBY6QL90GOMJA5GILCWWLT",         # Journey
            "lifecycle_1714094340032": "1I00EBMOY66ROGCBY63E4BLUKMID71508EXR"        # Lifecycle
        }
        
        # Display name mappings for debugging
        self.touchpoint_display_names = {
            "12980f6d-797c-47da-ab58-78f3a3e5c954": "SCANNER MAINTENANCE",
            "16a15fcd-d735-49a7-8b14-ca279e6dba5a": "MEND CONTAINER",
            "30989b43-ff62-486c-b4db-f89c3106abba": "ACCOUNT REVIEW",
            "38bb62d0-4b50-4a5f-906c-6edbfe4786db": "MEND SCA",
            "38ce7c4e-374f-42db-94e8-23ba48d4b1e3": "ESCALATION",
            "6cc323e4-4155-48bf-964c-ec75ee70e96b": "MEND PLATFORM ACCESS",
            "7c285778-76be-4543-b8a1-9066ee5a1ad4": "MEND SAST",
            "9165fe90-5bc2-480d-98b2-76f3926c8e23": "SUPPORT",
            "a2d4c216-8d7b-4f24-9cdf-3c4bc0226f94": "MARKETING",
            "b1120b9a-85e9-4c2c-b7e7-9214ac3baaea": "NPS",
            "c5d4cd04-1224-4ad3-8696-d2fb70ea85be": "PRODUCT MANAGEMENT ENGAGEMENT",
            "cbd1f1f4-229a-4e1b-b4a3-a091e90932cc": "ONBOARDING",
            "ceb3f746-7099-4083-bc18-7d941699c6b5": "REACHABILITY (SCA)",
            "cefe339e-50d6-434d-a126-d66d652ce06c": "MEND AI",
            "ddf60bee-a1d2-4b03-b1bd-1f59eef0e214": "OUTAGE",
            "e43ae486-e0b8-4e2c-a8f3-fe17821b003d": "CADENCE",
            "e4b6a6cf-0833-4a60-aac2-52759e73e9ab": "RENEWAL",
            "eb5d8037-0f63-452e-8007-977e91e061b5": "REFERENCE",
            "ed358b0e-5cad-42e6-80be-04df0db7dc2c": "FEE",
            "f9f8cc62-1436-4f96-afe7-ef77a2ff18ed": "SAST CLOUD MIGRATION"
        }
        
        self.flow_type_display_names = {
            "renewal": "Renewal",
            "support": "Support",
            "upsell": "Upsell",
            "escalation": "Escalation",
            "onboarding_101": "Onboarding",
            "adoption": "Adoption",
            "risk_1560979263618": "Risk",
            "product_transition_1630456786595": "Product Transition",
            "intelligence_1561140678082": "Intelligence",
            "services_1631204797082": "Services",
            "inbound_1631240727463": "Inbound",
            "design_partner_1635451768576": "Design Partner",
            "nps_1654*********": "NPS",
            "business_review_1628207371592": "Business Review",
            "journey_1713548309375": "Journey",
            "lifecycle_1714094340032": "Lifecycle"
        }
    
    # ... (keeping all existing methods from the original migrator) ...
    
    def load_config(self, config_file):
        if config_file and os.path.exists(config_file):
            with open(config_file, 'r') as f:
                return json.load(f)
        return {}
    
    def get_totango_activities(self, account_ids: List[str]) -> List[Dict]:
        logger.info(f"Extracting activities with meeting_type from {len(account_ids)} Totango accounts...")
        
        all_activities = []
        
        for account_id in account_ids:
            try:
                url = f"{self.config['totango']['url']}/t01/mend/api/v2/events/"
                params = {
                    'account_id': account_id,
                    'include_formatting': 'true'
                }
                
                response = self.session.get(
                    url,
                    headers=self.config['totango']['headers'],
                    params=params,
                    timeout=30
                )
                
                if response.status_code == 200:
                    events = response.json()
                    
                    # Filter for events with meeting_type property ONLY
                    meeting_events = []
                    for event in events:
                        properties = event.get('properties', {})
                        if 'meeting_type' in properties and properties['meeting_type']:
                            event['sourceAccountId'] = account_id
                            meeting_events.append(event)
                    
                    all_activities.extend(meeting_events)
                    logger.info(f"Account {account_id}: {len(meeting_events)} activities with meeting_type")
                    
                else:
                    logger.error(f"Failed for account {account_id}: {response.status_code}")
                    
            except Exception as e:
                logger.error(f"Error with account {account_id}: {e}")
                
        logger.info(f"Total activities with meeting_type extracted: {len(all_activities)}")
        return all_activities
    
    def get_gainsight_users(self) -> Dict[str, Dict]:
        logger.info("Loading Gainsight users...")
        
        users = {}
        try:
            all_users = []
            page_number = 1
            
            while True:
                payload = {
                    'limit': 50,
                    'pageNumber': page_number,
                    'searchString': '',
                    'clause': None,
                    'fields': ['Email', 'Gsid', 'Name']
                }
                
                response = self.session.post(
                    f"{self.config['gainsight']['url']}/v1/dataops/gdm/list?object=GsUser",
                    json=payload,
                    headers=self.config['gainsight']['headers'],
                    timeout=30
                )
                
                if response.status_code != 200:
                    break
                    
                users_data = response.json().get('data', {}).get('data', [])
                if not users_data:
                    break
                    
                all_users.extend(users_data)
                page_number += 1
                
            for user in all_users:
                email = user.get('Email', '').lower()
                if email:
                    users[email] = {
                        'id': user.get('Gsid', ''),
                        'name': user.get('Name', ''),
                        'email': user.get('Email', '')
                    }
                    
            logger.info(f"Loaded {len(users)} users")
            
        except Exception as e:
            logger.error(f"Failed to load users: {e}")
            
        return users
    
    def get_gainsight_activity_types(self, company_id: str) -> Dict[str, str]:
        logger.info("Loading activity types...")
        
        try:
            url = f"{self.config['gainsight']['url']}/v1/ant/forms"
            params = {
                'context': 'Company',
                'contextId': company_id,
                'showHidden': 'false'
            }
            
            response = self.session.get(
                url,
                params=params,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if 'data' in data and 'activityTypes' in data['data']:
                    activity_types = data['data']['activityTypes']
                    
                    type_mapping = {}
                    for activity_type in activity_types:
                        name = activity_type.get('name', '').upper()
                        type_id = activity_type.get('id', '')
                        if name and type_id:
                            type_mapping[name] = type_id
                    
                    logger.info(f"Successfully loaded {len(type_mapping)} activity types")
                    self.results['activity_types'] = type_mapping
                    
                    return type_mapping
                else:
                    logger.error(f"Unexpected response structure. Keys: {list(data.keys())}")
                    
            else:
                logger.error(f"API call failed: {response.status_code}")
                
        except Exception as e:
            logger.error(f"Exception loading activity types: {e}")
            
        return {}
    
    # ============================================================================
    # NEW ATTACHMENT HANDLING METHODS
    # ============================================================================
    
    def extract_totango_attachments(self, activity: Dict) -> List[Dict]:
        """Extract and parse attachments from Totango activity"""
        attachments = []
        
        try:
            properties = activity.get('properties', {})
            assets = properties.get('assets', [])
            
            if not assets:
                return attachments
            
            logger.info(f"🔗 Processing {len(assets)} assets from activity")
            
            for asset_str in assets:
                try:
                    # Parse the JSON string
                    if isinstance(asset_str, str):
                        asset = json.loads(asset_str)
                    else:
                        asset = asset_str
                    
                    attachment_info = self.parse_totango_asset(asset)
                    if attachment_info:
                        attachments.append(attachment_info)
                        self.results['attachments_processed'] += 1
                        
                except json.JSONDecodeError as e:
                    logger.error(f"❌ Failed to parse asset JSON: {e}")
                except Exception as e:
                    logger.error(f"❌ Error processing asset: {e}")
            
            logger.info(f"✅ Extracted {len(attachments)} valid attachments")
            
        except Exception as e:
            logger.error(f"❌ Error extracting attachments: {e}")
        
        return attachments
    
    def parse_totango_asset(self, asset: Dict) -> Optional[Dict]:
        """Parse individual Totango asset into standardized format"""
        try:
            asset_type = asset.get('asset_type', '')
            name = asset.get('name', 'Attachment')
            asset_url = asset.get('asset_url', '')
            download_path = asset.get('downloadPath', '')
            extension = asset.get('extension', '')
            asset_id = asset.get('id', '')
            
            # Determine the actual URL/path
            if asset_type == 'link' and asset_url:
                # External link (Gong, Zoom, Google Docs, etc.)
                return {
                    'type': 'link',
                    'name': name,
                    'url': asset_url,
                    'extension': extension,
                    'totango_id': asset_id,
                    'needs_upload': False
                }
            elif asset_type == 'file' and download_path:
                # File that needs to be downloaded from Totango using token-based auth
                return {
                    'type': 'file',
                    'name': name,
                    'url': '',  # Will be populated by token request
                    'extension': extension,
                    'totango_id': asset_id,
                    'download_path': download_path,
                    'needs_upload': True
                }
            else:
                logger.warning(f"⚠️ Skipping asset with missing URL/path: {name} (type: {asset_type})")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error parsing asset: {e}")
            return None
    
    def extract_links_from_content(self, content: str) -> List[Dict]:
        """Extract embedded links from note content"""
        links = []
        
        if not content:
            return links
        
        try:
            # Extract standard URLs from text/HTML
            url_pattern = r'https?://[^\s<>"\'()]+[^\s<>"\'().]'
            urls = re.findall(url_pattern, content)
            
            # Extract URLs from HTML href attributes
            href_pattern = r'href=["\']([^"\']+)["\']'
            href_urls = re.findall(href_pattern, content, re.IGNORECASE)
            
            # Combine and deduplicate
            all_urls = list(set(urls + href_urls))
            
            for url in all_urls:
                # Skip base64 data URLs and invalid URLs
                if url.startswith('data:') or url.startswith('cid:'):
                    continue
                
                links.append({
                    'type': 'embedded_link',
                    'name': self.get_link_display_name(url),
                    'url': url,
                    'needs_upload': False
                })
            
            if links:
                logger.info(f"🔗 Extracted {len(links)} embedded links from content")
                
        except Exception as e:
            logger.error(f"❌ Error extracting links from content: {e}")
        
        return links
    
    def debug_attachment_download(self, attachment: Dict) -> None:
        """Debug attachment download to understand the issue"""
        logger.info(f"🔍 DEBUG: Attachment Analysis for {attachment['name']}")
        logger.info(f"  Type: {attachment['type']}")
        logger.info(f"  Totango ID: {attachment.get('totango_id', 'N/A')}")
        logger.info(f"  Download Path: {attachment.get('download_path', 'N/A')}")
        logger.info(f"  Extension: {attachment.get('extension', 'N/A')}")
        
        # Try multiple download approaches
        download_path = attachment.get('download_path', '')
        asset_id = attachment.get('totango_id', '')
        
        if download_path and asset_id:
            # Test different URL constructions
            approaches = [
                f"{self.config['totango']['url']}{download_path}",
                f"{self.config['totango']['url']}/t01/mend{download_path}",
                f"https://assets-proxy.totango.com/api/v2/assets{download_path}",
                f"{self.config['totango']['url']}/t01/mend/api/v2/assets/{asset_id}/download"
            ]
            
            for i, url in enumerate(approaches, 1):
                logger.info(f"  🧪 Test URL {i}: {url}")
                try:
                    response = self.session.head(url, headers=self.config['totango']['headers'], timeout=10)
                    logger.info(f"    Response: {response.status_code}")
                except Exception as e:
                    logger.info(f"    Error: {e}")
    
    def try_alternative_download_methods(self, attachment: Dict) -> Optional[bytes]:
        """Try alternative download methods for files that fail with token approach"""
        logger.info(f"🔄 Trying alternative download methods for {attachment['name']}")
        
        download_path = attachment.get('download_path', '')
        asset_id = attachment.get('totango_id', '')
        
        # Method 1: Direct path with base URL
        if download_path:
            try:
                direct_url = f"{self.config['totango']['url']}{download_path}"
                logger.info(f"🧪 Method 1 - Direct path: {direct_url}")
                
                response = self.session.get(
                    direct_url,
                    headers=self.config['totango']['headers'],
                    timeout=60
                )
                
                if response.status_code == 200:
                    logger.info(f"✅ Method 1 SUCCESS: Downloaded {len(response.content)} bytes")
                    return response.content
                else:
                    logger.info(f"❌ Method 1 failed: {response.status_code}")
                    
            except Exception as e:
                logger.info(f"❌ Method 1 error: {e}")
        
        # Method 2: Try assets proxy without token (might work with session cookies)
        if download_path:
            try:
                path_parts = download_path.strip('/').split('/')
                if len(path_parts) >= 2:
                    service_id = path_parts[0]
                    file_path = '/'.join(path_parts[1:])
                    
                    proxy_url = f"https://assets-proxy.totango.com/api/v2/assets/{service_id}/{file_path}"
                    logger.info(f"🧪 Method 2 - Assets proxy: {proxy_url}")
                    
                    response = self.session.get(
                        proxy_url,
                        headers=self.config['totango']['headers'],
                        timeout=60
                    )
                    
                    if response.status_code == 200:
                        logger.info(f"✅ Method 2 SUCCESS: Downloaded {len(response.content)} bytes")
                        return response.content
                    else:
                        logger.info(f"❌ Method 2 failed: {response.status_code}")
                        
            except Exception as e:
                logger.info(f"❌ Method 2 error: {e}")
        
        # Method 3: Use asset ID in different API endpoints
        if asset_id:
            try:
                asset_api_url = f"{self.config['totango']['url']}/t01/mend/api/v2/assets/{asset_id}"
                logger.info(f"🧪 Method 3 - Asset API: {asset_api_url}")
                
                response = self.session.get(
                    asset_api_url,
                    headers=self.config['totango']['headers'],
                    timeout=60
                )
                
                if response.status_code == 200:
                    # This might return metadata with the actual download URL
                    result = response.json()
                    actual_download_url = result.get('downloadUrl') or result.get('url')
                    
                    if actual_download_url:
                        logger.info(f"🔗 Found download URL in metadata: {actual_download_url}")
                        
                        file_response = self.session.get(
                            actual_download_url,
                            headers=self.config['totango']['headers'],
                            timeout=60
                        )
                        
                        if file_response.status_code == 200:
                            logger.info(f"✅ Method 3 SUCCESS: Downloaded {len(file_response.content)} bytes")
                            return file_response.content
                        else:
                            logger.info(f"❌ Method 3 file download failed: {file_response.status_code}")
                    else:
                        logger.info(f"❌ Method 3: No download URL in metadata")
                else:
                    logger.info(f"❌ Method 3 failed: {response.status_code}")
                    
            except Exception as e:
                logger.info(f"❌ Method 3 error: {e}")
        
        logger.error(f"❌ All download methods failed for {attachment['name']}")
        return None
    
    def get_link_display_name(self, url: str) -> str:
        """Generate a display name for a URL"""
        try:
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            
            # Common platform mappings
            if 'gong.io' in domain:
                return 'Gong Recording'
            elif 'zoom.us' in domain:
                return 'Zoom Recording'
            elif 'docs.google.com' in domain:
                return 'Google Document'
            elif 'drive.google.com' in domain:
                return 'Google Drive File'
            elif 'github.com' in domain:
                return 'GitHub Link'
            elif 'confluence' in domain:
                return 'Confluence Page'
            else:
                return f"Link: {domain}"
                
        except Exception:
            return "Web Link"
        """Generate a display name for a URL"""
        try:
            parsed = urlparse(url)
            domain = parsed.netloc.lower()
            
            # Common platform mappings
            if 'gong.io' in domain:
                return 'Gong Recording'
            elif 'zoom.us' in domain:
                return 'Zoom Recording'
            elif 'docs.google.com' in domain:
                return 'Google Document'
            elif 'drive.google.com' in domain:
                return 'Google Drive File'
            elif 'github.com' in domain:
                return 'GitHub Link'
            elif 'confluence' in domain:
                return 'Confluence Page'
            else:
                return f"Link: {domain}"
                
        except Exception:
            return "Web Link"
    
    def get_download_token(self) -> Optional[str]:
        """Get a JWT download token from config or extract from session"""
        try:
            # Priority 1: Check if token is manually configured
            manual_token = self.config.get('totango', {}).get('jwt_token', '')
            if manual_token and len(manual_token) > 50:
                logger.info("🔑 Using manually configured JWT token")
                return manual_token
            
            # Priority 2: Try extracting from session headers/cookies
            return self.extract_token_from_headers()
            
        except Exception as e:
            logger.error(f"❌ Error getting download token: {e}")
            return None
    
    def extract_token_from_headers(self) -> Optional[str]:
        """Extract token information from session headers/cookies"""
        try:
            headers = self.config['totango']['headers']
            
            # Check if there's already a token in Authorization header
            auth_header = headers.get('Authorization', '')
            if 'Bearer ' in auth_header:
                token = auth_header.replace('Bearer ', '')
                if self.is_valid_jwt(token):
                    logger.info(f"🔑 Found token in Authorization header")
                    return token
            
            # Check cookies for JWT-like patterns
            cookie_header = headers.get('Cookie', '')
            if cookie_header:
                import re
                jwt_pattern = r'[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+'
                matches = re.findall(jwt_pattern, cookie_header)
                
                for match in matches:
                    if len(match) > 100 and self.is_valid_jwt(match):
                        logger.info(f"🔑 Found JWT in cookies")
                        return match
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error extracting token from headers: {e}")
            return None
    
    def is_valid_jwt(self, token: str) -> bool:
        """Check if a string looks like a valid JWT token"""
        if not token or len(token) < 50:
            return False
        
        # JWT has 3 parts separated by dots
        parts = token.split('.')
        if len(parts) != 3:
            return False
        
        try:
            # Try to decode the header (first part)
            import base64
            header = parts[0]
            # Add padding if needed
            header += '=' * (4 - len(header) % 4)
            decoded_header = base64.b64decode(header, validate=True)
            header_json = json.loads(decoded_header)
            
            # Should have 'alg' and 'typ' fields
            if 'alg' in header_json and 'typ' in header_json:
                return True
                
        except:
            pass
        
        return False
    
    def generate_download_url_with_token(self, download_path: str, token: str) -> str:
        """Generate the assets-proxy URL with JWT token parameter"""
        try:
            # Parse the download path to construct assets-proxy URL
            path_parts = download_path.strip('/').split('/')
            service_id = path_parts[0]  # e.g., "45606"
            file_path = '/'.join(path_parts[1:])  # remaining path
            
            # Construct the assets-proxy URL with token (like the user's example)
            base_url = "https://assets-proxy.totango.com/api/v2/assets"
            download_url = f"{base_url}/{service_id}/{file_path}?token={token}"
            
            return download_url
            
        except Exception as e:
            logger.error(f"❌ Error generating download URL: {e}")
            return ""
    
    def download_totango_file(self, attachment: Dict) -> Optional[bytes]:
        """Download file from Totango using JWT token with assets-proxy URL"""
        try:
            asset_id = attachment.get('totango_id', '')
            download_path = attachment.get('download_path', '')
            
            logger.info(f"📥 Downloading: {attachment['name']} (ID: {asset_id})")
            
            # Step 1: Get JWT token
            token = self.get_download_token()
            
            if not token:
                logger.error(f"❌ Could not obtain JWT token for {attachment['name']}")
                return None
            
            # Step 2: Generate assets-proxy URL with token
            download_url = self.generate_download_url_with_token(download_path, token)
            
            if not download_url:
                logger.error(f"❌ Could not generate download URL for {attachment['name']}")
                return None
            
            logger.info(f"🔗 Using token URL: {download_url[:100]}...")
            
            # Step 3: Download using the token URL
            response = self.session.get(
                download_url,
                timeout=60,
                stream=True  # Use streaming for large files
            )
            
            if response.status_code == 200:
                file_content = response.content
                logger.info(f"✅ Successfully downloaded {len(file_content)} bytes")
                return file_content
            elif response.status_code == 401:
                logger.error(f"❌ Token authentication failed (401) - Token may be expired")
                return None
            elif response.status_code == 403:
                logger.error(f"❌ Access forbidden (403) - Insufficient permissions")
                return None
            elif response.status_code == 404:
                logger.error(f"❌ File not found (404) - File may have been deleted")
                return None
            else:
                logger.error(f"❌ Download failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error downloading file: {e}")
            return None
    
    def upload_to_gainsight(self, attachment: Dict, file_content: bytes = None, activity_id: str = None) -> Optional[str]:
        """Upload attachment to Gainsight using the EXACT format from successful browser request"""
        try:
            if attachment['type'] == 'link' or not attachment.get('needs_upload', False):
                # For links, we'll add them directly to the activity
                logger.info(f"🔗 Link attachment: {attachment['name']} -> {attachment['url']}")
                return attachment['url']
            
            if not file_content:
                logger.error(f"❌ No file content for upload: {attachment['name']}")
                return None
            
            logger.info(f"📤 Uploading to Gainsight: {attachment['name']}")
            logger.info(f"  File size: {len(file_content)} bytes")
            logger.info(f"  File type: {self.get_mime_type(attachment['extension'])}")
            
            # Prepare the multipart form data with EXACT Gainsight format
            files = {
                'file': (
                    attachment['name'], 
                    file_content, 
                    self.get_mime_type(attachment['extension'])
                )
            }
            
            # Form data using the EXACT format from successful browser request
            form_data = {
                'entityId': activity_id or '',  # The activity we're attaching to
                'contexts': json.dumps([{
                    'id': self.config['gainsight']['company_id'],
                    'base': True,
                    'obj': 'Company',
                    'lbl': self.config['gainsight']['company_name'],
                    'eid': None,
                    'eobj': 'Account',
                    'eurl': None,
                    'esys': 'SALESFORCE',
                    'dsp': True
                }]),
                'source': 'C360',
                'user': json.dumps({
                    'id': self.config['gainsight']['user_id'],
                    'obj': 'User',
                    'name': self.config['gainsight']['user_name'],
                    'email': self.config['gainsight']['user_email'],
                    'eid': None,
                    'eobj': 'User',
                    'epp': None,
                    'esys': 'SALESFORCE',
                    'sys': 'GAINSIGHT',
                    'pp': ''
                }),
                'type': 'DEFAULT'
            }
            
            # Use original endpoint with proper browser headers
            upload_url = f"{self.config['gainsight']['url']}/v1/ant/attachments"
            
            # Create headers that match successful browser request
            upload_headers = {
                'Accept': 'application/json, text/plain, */*',
                'Cookie': self.config['gainsight']['headers'].get('Cookie', ''),
                'Origin': self.config['gainsight']['url'],
                'Referer': f"{self.config['gainsight']['url']}/v1/ui/customersuccess360?cid={self.config['gainsight']['company_id']}",
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-Gs-Host': 'GAINSIGHT'
            }
            
            logger.info(f"📡 Uploading to: {upload_url}")
            logger.info(f"📋 Using exact Gainsight format with entityId: {activity_id}")
            
            # Make the upload request with exact format
            response = self.session.post(
                upload_url,
                files=files,
                data=form_data,
                headers=upload_headers,
                timeout=120
            )
            
            logger.info(f"📥 Upload response: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    logger.info(f"📋 Response structure: {list(result.keys())}")
                    
                    if result.get('result') and result.get('data'):
                        attachment_data = result['data']
                        attachment_id = attachment_data.get('id')
                        seq_id = attachment_data.get('seqId')
                        file_url = attachment_data.get('url', '')
                        
                        if attachment_id:
                            logger.info(f"✅ Upload successful!")
                            logger.info(f"  Attachment ID: {attachment_id}")
                            logger.info(f"  Sequence ID: {seq_id}")
                            logger.info(f"  S3 URL: {file_url[:100]}...")
                            logger.info(f"  File size: {attachment_data.get('size', 'unknown')} bytes")
                            
                            self.results['attachments_uploaded'] += 1
                            return attachment_id
                        else:
                            logger.error(f"❌ No attachment ID in successful response")
                            logger.error(f"Response data: {attachment_data}")
                    else:
                        logger.error(f"❌ Unexpected response structure")
                        logger.error(f"Response: {result}")
                    
                except json.JSONDecodeError as e:
                    logger.error(f"❌ Invalid JSON response: {e}")
                    logger.error(f"Response text: {response.text[:500]}")
                    
            elif response.status_code == 400:
                logger.error(f"❌ Bad Request (400) - Check field mapping")
                logger.error(f"Response: {response.text[:500]}")
                
            elif response.status_code == 405:
                logger.error(f"❌ Method Not Allowed (405) - Check headers")
                logger.error(f"Response: {response.text[:500]}")
                
            elif response.status_code == 401:
                logger.error(f"❌ Unauthorized (401) - Check authentication")
            elif response.status_code == 403:
                logger.error(f"❌ Forbidden (403) - Insufficient permissions")
            else:
                logger.error(f"❌ Upload failed: {response.status_code}")
                logger.error(f"Response: {response.text[:500]}")
            
            self.results['attachments_failed'] += 1
            return None
                
        except Exception as e:
            logger.error(f"❌ Error uploading to Gainsight: {e}")
            self.results['attachments_failed'] += 1
            return None
        """Upload attachment to Gainsight and return attachment ID"""
        try:
            if attachment['type'] == 'link' or not attachment.get('needs_upload', False):
                # For links, we'll add them directly to the activity
                logger.info(f"🔗 Link attachment: {attachment['name']} -> {attachment['url']}")
                return attachment['url']
            
            if not file_content:
                logger.error(f"❌ No file content for upload: {attachment['name']}")
                return None
            
            logger.info(f"📤 Uploading to Gainsight: {attachment['name']}")
            logger.info(f"  File size: {len(file_content)} bytes")
            logger.info(f"  File type: {self.get_mime_type(attachment['extension'])}")
            
            # Prepare the multipart form data in the exact format Gainsight expects
            files = {
                'file': (
                    attachment['name'], 
                    file_content, 
                    self.get_mime_type(attachment['extension'])
                )
            }
            
            # Form data with proper context structure (matching the successful response format)
            form_data = {
                'contexts': json.dumps([{
                    'id': self.config['gainsight']['company_id'],
                    'base': True,
                    'obj': 'Company',
                    'lbl': self.config['gainsight']['company_name'],
                    'eid': None,
                    'eobj': 'Account',
                    'eurl': None,
                    'esys': 'SALESFORCE',
                    'dsp': True
                }]),
                'source': 'C360'
            }
            
            # Use the CORRECT endpoint - debug showed /upload works, not /attachments
            upload_url = f"{self.config['gainsight']['url']}/v1/ant/attachments/upload"
            
            # Create headers for multipart upload (remove Content-Type to let requests set it)
            upload_headers = {}
            for key, value in self.config['gainsight']['headers'].items():
                if key.lower() not in ['content-type', 'content-length']:
                    upload_headers[key] = value
            
            logger.info(f"📡 Uploading to: {upload_url}")
            logger.info(f"📋 Context: Company ID {self.config['gainsight']['company_id']}")
            
            # Make the upload request
            response = self.session.post(
                upload_url,
                files=files,
                data=form_data,
                headers=upload_headers,
                timeout=120
            )
            
            logger.info(f"📥 Upload response: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    logger.info(f"📋 Response structure: {list(result.keys())}")
                    
                    if result.get('result') and result.get('data'):
                        attachment_data = result['data']
                        attachment_id = attachment_data.get('id')
                        seq_id = attachment_data.get('seqId')
                        file_url = attachment_data.get('url', '')
                        
                        if attachment_id:
                            logger.info(f"✅ Upload successful!")
                            logger.info(f"  Attachment ID: {attachment_id}")
                            logger.info(f"  Sequence ID: {seq_id}")
                            logger.info(f"  File URL: {file_url[:100]}...")
                            logger.info(f"  File size: {attachment_data.get('size', 'unknown')} bytes")
                            
                            self.results['attachments_uploaded'] += 1
                            return attachment_id
                        else:
                            logger.error(f"❌ No attachment ID in successful response")
                            logger.error(f"Response data: {attachment_data}")
                    else:
                        logger.error(f"❌ Unexpected response structure")
                        logger.error(f"Response: {result}")
                    
                except json.JSONDecodeError as e:
                    logger.error(f"❌ Invalid JSON response: {e}")
                    logger.error(f"Response text: {response.text[:500]}")
                    
            elif response.status_code == 400:
                logger.error(f"❌ Bad Request (400) - Invalid request format")
                logger.error(f"Response: {response.text[:500]}")
                
                # Log debugging information for 400 errors
                logger.info(f"🔍 DEBUG INFO:")
                logger.info(f"  File name: {attachment['name']}")
                logger.info(f"  Content type: {self.get_mime_type(attachment['extension'])}")
                logger.info(f"  File size: {len(file_content)} bytes")
                logger.info(f"  Company ID: {self.config['gainsight']['company_id']}")
                
            elif response.status_code == 401:
                logger.error(f"❌ Unauthorized (401) - Check Gainsight authentication")
            elif response.status_code == 403:
                logger.error(f"❌ Forbidden (403) - Insufficient permissions")
            elif response.status_code == 413:
                logger.error(f"❌ File too large (413) - {len(file_content)} bytes")
            else:
                logger.error(f"❌ Upload failed: {response.status_code}")
                logger.error(f"Response: {response.text[:500]}")
            
            self.results['attachments_failed'] += 1
            return None
                
        except Exception as e:
            logger.error(f"❌ Error uploading to Gainsight: {e}")
            self.results['attachments_failed'] += 1
            return None
    
    def get_mime_type(self, extension: str) -> str:
        """Get MIME type for file extension"""
        extension = extension.lower().lstrip('.')
        
        mime_types = {
            'pdf': 'application/pdf',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls': 'application/vnd.ms-excel',
            'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'ppt': 'application/vnd.ms-powerpoint',
            'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'gif': 'image/gif',
            'bmp': 'image/bmp',
            'txt': 'text/plain',
            'csv': 'text/csv',
            'json': 'application/json',
            'zip': 'application/zip',
            'rar': 'application/x-rar-compressed'
        }
        
        return mime_types.get(extension, 'application/octet-stream')
    
    def process_all_attachments(self, activity: Dict, activity_id: str = None) -> List[Dict]:
        """Process all attachments for an activity and return Gainsight attachment list"""
        gainsight_attachments = []
        
        try:
            # 1. Extract attachments from Totango assets
            totango_attachments = self.extract_totango_attachments(activity)
            
            # 2. Extract embedded links from note content
            note_content = self.extract_content(activity)
            embedded_links = self.extract_links_from_content(note_content)
            
            # 3. Combine all attachments
            all_attachments = totango_attachments + embedded_links
            
            if not all_attachments:
                return gainsight_attachments
            
            logger.info(f"🔗 Processing {len(all_attachments)} total attachments for activity: {activity_id}")
            
            # 4. Process each attachment
            for attachment in all_attachments:
                try:
                    if attachment['needs_upload']:
                        # Download from Totango and upload to Gainsight with activity ID
                        file_content = self.download_totango_file(attachment)
                        if file_content:
                            attachment_id = self.upload_to_gainsight(attachment, file_content, activity_id)
                            if attachment_id:
                                gainsight_attachments.append({
                                    'id': attachment_id,
                                    'name': attachment['name'],
                                    'seqId': attachment_id,
                                    'published': True,
                                    'removed': False
                                })
                    else:
                        # For links, we'll include them in the note content rather than as attachments
                        # since Gainsight typically handles external links differently
                        logger.info(f"🔗 Keeping link in content: {attachment['name']}")
                        
                except Exception as e:
                    logger.error(f"❌ Error processing attachment {attachment['name']}: {e}")
                    continue
            
            logger.info(f"✅ Processed {len(gainsight_attachments)} attachments for upload")
            
        except Exception as e:
            logger.error(f"❌ Error processing attachments: {e}")
        
        return gainsight_attachments
        """Process all attachments for an activity and return Gainsight attachment list"""
        gainsight_attachments = []
        
        try:
            # 1. Extract attachments from Totango assets
            totango_attachments = self.extract_totango_attachments(activity)
            
            # 2. Extract embedded links from note content
            note_content = self.extract_content(activity)
            embedded_links = self.extract_links_from_content(note_content)
            
            # 3. Combine all attachments
            all_attachments = totango_attachments + embedded_links
            
            if not all_attachments:
                return gainsight_attachments
            
            logger.info(f"🔗 Processing {len(all_attachments)} total attachments")
            
            # 4. Process each attachment
            for attachment in all_attachments:
                try:
                    if attachment['needs_upload']:
                        # Download from Totango and upload to Gainsight
                        file_content = self.download_totango_file(attachment)
                        if file_content:
                            attachment_id = self.upload_to_gainsight(attachment, file_content)
                            if attachment_id:
                                gainsight_attachments.append({
                                    'id': attachment_id,
                                    'name': attachment['name'],
                                    'seqId': attachment_id,
                                    'published': True,
                                    'removed': False
                                })
                    else:
                        # For links, we'll include them in the note content rather than as attachments
                        # since Gainsight typically handles external links differently
                        logger.info(f"🔗 Keeping link in content: {attachment['name']}")
                        
                except Exception as e:
                    logger.error(f"❌ Error processing attachment {attachment['name']}: {e}")
                    continue
            
            logger.info(f"✅ Processed {len(gainsight_attachments)} attachments for upload")
            
        except Exception as e:
            logger.error(f"❌ Error processing attachments: {e}")
        
        return gainsight_attachments
    
    # ============================================================================
    # EXISTING METHODS (keeping all from the original)
    # ============================================================================
    
    def map_touchpoint_reason(self, totango_activity: Dict) -> Optional[str]:
        """Map Totango touchpoint tags to Gainsight touchpoint reason IDs using direct mapping"""
        properties = totango_activity.get('properties', {})
        
        # Check for touchpoint_tags in the activity
        touchpoint_tags = properties.get('touchpoint_tags')
        if touchpoint_tags:
            # Handle different formats (string or list)
            if isinstance(touchpoint_tags, str):
                tag_ids = [touchpoint_tags]
            elif isinstance(touchpoint_tags, list):
                tag_ids = touchpoint_tags
            else:
                tag_ids = []
            
            # Map the IDs directly to Gainsight IDs using our corrected mapping
            mapped_gainsight_ids = []
            for tag_id in tag_ids:
                if tag_id in self.touchpoint_id_mapping:
                    gainsight_id = self.touchpoint_id_mapping[tag_id]
                    display_name = self.touchpoint_display_names.get(tag_id, tag_id)
                    mapped_gainsight_ids.append(gainsight_id)
                    logger.info(f"✅ Mapped touchpoint: {tag_id} ({display_name}) -> {gainsight_id}")
                    self.results['mappings_applied']['touchpoint_reason_mappings'] += 1
                else:
                    logger.warning(f"⚠️ Unmapped touchpoint tag: {tag_id}")
            
            if mapped_gainsight_ids:
                # Return the first mapped ID (or you could return all as a list if Gainsight supports multiple)
                return mapped_gainsight_ids[0]
        
        return None
    
    def map_flow_type(self, totango_activity: Dict) -> Optional[str]:
        """Map Totango activity_type_id to Gainsight flow type IDs using direct mapping"""
        properties = totango_activity.get('properties', {})
        
        # Check for activity_type_id field (this is the correct field for flow types)
        activity_type_id = properties.get('activity_type_id')
        if activity_type_id and activity_type_id in self.flow_type_id_mapping:
            gainsight_id = self.flow_type_id_mapping[activity_type_id]
            display_name = self.flow_type_display_names.get(activity_type_id, activity_type_id)
            logger.info(f"✅ Mapped flow type: {activity_type_id} ({display_name}) -> {gainsight_id}")
            self.results['mappings_applied']['flow_type_mappings'] += 1
            return gainsight_id
        
        # Check for other possible flow type fields as fallback
        for field_name in ['flow_type', 'flow_type_id', 'type_id']:
            if field_name in properties:
                flow_type_id = properties[field_name]
                if flow_type_id in self.flow_type_id_mapping:
                    gainsight_id = self.flow_type_id_mapping[flow_type_id]
                    logger.info(f"✅ Mapped flow type (via {field_name}): {flow_type_id} -> {gainsight_id}")
                    self.results['mappings_applied']['flow_type_mappings'] += 1
                    return gainsight_id
        
        return None
    
    def map_activity_type(self, meeting_type_id: str, gainsight_types: Dict[str, str]) -> str:
        """Map Totango meeting type ID to Gainsight activity type ID"""
        if not meeting_type_id:
            return gainsight_types.get('EMAIL', list(gainsight_types.values())[0] if gainsight_types else '')
        
        # Simple mapping logic - could be enhanced based on your specific needs
        mappings = {
            # Common meeting type patterns
            "email": "EMAIL",
            "call": "CALL", 
            "meeting": "MEETING",
            "note": "UPDATE",
            "update": "UPDATE",
            "feedback": "FEEDBACK",
            "slack": "SLACK",
            "inbound": "INBOUND"
        }
        
        # Pattern matching
        clean_type = meeting_type_id.lower()
        for pattern, gainsight_type in mappings.items():
            if pattern in clean_type and gainsight_type in gainsight_types:
                logger.info(f"Mapped activity type: {meeting_type_id} -> {gainsight_type}")
                return gainsight_types[gainsight_type]
        
        # Default fallback
        if 'UPDATE' in gainsight_types:
            logger.info(f"Default mapping: {meeting_type_id} -> UPDATE")
            return gainsight_types['UPDATE']
            
        return list(gainsight_types.values())[0] if gainsight_types else ''
    
    def transform_activity(self, activity: Dict, users: Dict, activity_types: Dict) -> Optional[Dict]:
        try:
            properties = activity.get('properties', {})
            
            subject = self.extract_subject(activity)
            content = self.extract_content(activity)
            meeting_type_id = properties.get('meeting_type', '')
            timestamp = activity.get('timestamp', '')
            
            # Map meeting type ID to Gainsight activity type ID
            activity_type_id = self.map_activity_type(meeting_type_id, activity_types)
            if not activity_type_id:
                return None
                
            activity_date = self.format_timestamp(timestamp)
            author_info = self.extract_author_info(activity, users)
            
            # Apply the corrected mappings
            touchpoint_reason_id = self.map_touchpoint_reason(activity)
            flow_type_id = self.map_flow_type(activity)
            
            # NEW: Process attachments separately after activity creation
            # Note: We'll need the Gainsight activity ID to link attachments
            # For now, create activity without attachments, then add them
            gainsight_attachments = []  # Will be populated after activity creation
            
            # Enhanced custom fields with direct ID mappings
            custom_fields = {
                'internalAttendees': [
                    {
                        'id': author_info['id'],
                        'obj': 'User',
                        'name': author_info['name'],
                        'email': author_info['email'],
                        'eid': None,
                        'eobj': 'User',
                        'epp': None,
                        'esys': 'SALESFORCE',
                        'sys': 'GAINSIGHT',
                        'pp': ''
                    }
                ],
                'externalAttendees': []
            }
            
            # Add touchpoint reason if mapped (using the correct Gainsight ID)
            if touchpoint_reason_id:
                custom_fields['Ant__Touchpoint_Reason__c'] = touchpoint_reason_id
                logger.info(f"✅ Applied touchpoint reason ID: {touchpoint_reason_id}")
            
            # Add flow type if mapped (using the correct Gainsight ID)
            if flow_type_id:
                custom_fields['Ant__Flow_Type__c'] = flow_type_id
                logger.info(f"✅ Applied flow type ID: {flow_type_id}")
            
            payload = {
                'lastModifiedByUser': {
                    'gsId': author_info['id'],
                    'name': author_info['name'],
                    'eid': None,
                    'esys': None,
                    'pp': ''
                },
                'note': {
                    'customFields': custom_fields,
                    'type': self.get_activity_type_name_from_id(activity_type_id, activity_types),
                    'subject': subject,
                    'activityDate': activity_date,
                    'content': content,
                    'plainText': self.strip_html(content),
                    'trackers': None
                },
                'mentions': [],
                'relatedRecords': {},
                'meta': {
                    'activityTypeId': activity_type_id,
                    'ctaId': None,
                    'source': 'C360',
                    'hasTask': False,
                    'emailSent': False,
                    'systemType': 'GAINSIGHT',
                    'notesTemplateId': None
                },
                'author': {
                    'id': author_info['id'],
                    'obj': 'User',
                    'name': author_info['name'],
                    'email': author_info['email'],
                    'eid': None,
                    'eobj': 'User',
                    'epp': None,
                    'esys': 'SALESFORCE',
                    'sys': 'GAINSIGHT',
                    'pp': ''
                },
                'syncedToSFDC': False,
                'tasks': [],
                'attachments': gainsight_attachments,  # NEW: Include processed attachments
                'contexts': [
                    {
                        'id': self.config['gainsight']['company_id'],
                        'base': True,
                        'obj': 'Company',
                        'lbl': self.config['gainsight']['company_name'],
                        'eid': None,
                        'eobj': 'Account',
                        'eurl': None,
                        'esys': 'SALESFORCE',
                        'dsp': True
                    }
                ]
            }
            
            return payload
            
        except Exception as e:
            logger.error(f"Transform error: {e}")
            return None
    
    def extract_subject(self, activity: Dict) -> str:
        properties = activity.get('properties', {})
        
        for field in ['subject', 'name', 'display_name', 'title']:
            if field in properties and properties[field]:
                return str(properties[field])[:200]
                
        activity_type = activity.get('type', 'Activity')
        return f"Totango: {activity_type.replace('_', ' ').title()}"
    
    def extract_content(self, activity: Dict) -> str:
        """Generate HTML content for activity"""
        # Check for note_content.text at TOP LEVEL first (most important)
        if 'note_content' in activity:
            note_content = activity['note_content']
            if isinstance(note_content, dict) and 'text' in note_content:
                text_content = note_content['text']
                if text_content and text_content.strip():
                    # Return the text as-is if it's already HTML, otherwise wrap in <p>
                    if text_content.startswith('<') and ('</p>' in text_content or '</div>' in text_content):
                        return text_content
                    else:
                        return f"<p>{text_content}</p>"

        # Fallback to properties if no note_content
        properties = activity.get('properties', {})
        for field in ['description', 'content', 'message', 'body', 'subject']:
            if field in properties and properties[field]:
                content = str(properties[field])
                if content.strip():
                    return f"<p>{content}</p>" if not content.startswith('<') else content
                    
        # Generate based on activity type if no content fields found
        activity_type = activity.get('type', 'activity')
        return f"<p>Totango Activity: {activity_type.replace('_', ' ')}</p>"
    
    def extract_author_info(self, activity: Dict, users: Dict) -> Dict:
        enriched_users = activity.get('enrichedUsers', [])
        if enriched_users and isinstance(enriched_users, list):
            user = enriched_users[0]
            if isinstance(user, dict):
                email = user.get('email', '').lower()
                if email and email in users:
                    return users[email]
                    
        author = activity.get('author', {})
        if isinstance(author, dict):
            email = author.get('email', '').lower()
            if email and email in users:
                return users[email]
                
        return {
            'id': self.config['gainsight']['user_id'],
            'name': self.config['gainsight']['user_name'],
            'email': self.config['gainsight']['user_email']
        }
    
    def format_timestamp(self, timestamp) -> str:
        try:
            if isinstance(timestamp, str):
                dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            else:
                if timestamp > 10**10:
                    timestamp = timestamp / 1000
                dt = datetime.fromtimestamp(timestamp)
                
            return dt.isoformat()
        except:
            return datetime.now().isoformat()
    
    def strip_html(self, html: str) -> str:
        if not html:
            return ""
        return re.sub(r'<[^>]*>', '', html)
    
    def get_activity_type_name_from_id(self, activity_type_id: str, activity_types: Dict[str, str]) -> str:
        """Get activity type name from ID for the 'type' field in note"""
        for name, type_id in activity_types.items():
            if type_id == activity_type_id:
                return name
        return "Activity"
    
    def create_draft(self, payload: Dict) -> Optional[str]:
        try:
            url = f"{self.config['gainsight']['url']}/v1/ant/v2/activity/drafts"
            
            response = self.session.post(
                url,
                json=payload,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                draft_data = response.json()
                draft_id = draft_data.get('data', {}).get('id')
                return draft_id
            else:
                logger.error(f"Draft failed: {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Draft error: {e}")
            return None
    
    def create_timeline_entry(self, payload: Dict, draft_id: str) -> bool:
        try:
            url = f"{self.config['gainsight']['url']}/v1/ant/v2/activity"
            
            timeline_payload = payload.copy()
            timeline_payload['id'] = draft_id
            
            response = self.session.post(
                url,
                json=timeline_payload,
                headers=self.config['gainsight']['headers'],
                timeout=30
            )
            
            if response.status_code == 200:
                return True
            else:
                logger.error(f"Timeline failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Timeline error: {e}")
            return False
    
    def migrate_single_activity(self, activity: Dict, users: Dict, activity_types: Dict) -> Dict:
        result = {
            'success': False,
            'activity_id': activity.get('id', 'unknown'),
            'account_id': activity.get('sourceAccountId', 'unknown'),
            'error': None,
            'attachments_processed': 0
        }
        
        try:
            # Step 1: Create activity payload (without attachments initially)
            payload = self.transform_activity(activity, users, activity_types)
            if not payload:
                result['error'] = 'Transform failed'
                return result
                
            # Step 2: Create draft and timeline entry
            draft_id = self.create_draft(payload)
            if not draft_id:
                result['error'] = 'Draft failed'
                return result
                
            if not self.create_timeline_entry(payload, draft_id):
                result['error'] = 'Timeline failed'
                return result
            
            # Step 3: Process and upload attachments using the draft_id as entityId
            gainsight_attachments = self.process_all_attachments(activity, draft_id)
            result['attachments_processed'] = len(gainsight_attachments)
            
            if gainsight_attachments:
                logger.info(f"✅ Activity created with {len(gainsight_attachments)} attachments")
            else:
                logger.info(f"✅ Activity created (no attachments)")
                
            result['success'] = True
            logger.info(f"✅ Migrated activity for {result['account_id']}")
                
        except Exception as e:
            result['error'] = str(e)
            
        return result
    
    def migrate_all_activities(self, account_ids: List[str]):
        logger.info("🚀 Starting ENHANCED migration with attachment support...")
        
        start_time = time.time()
        
        activities = self.get_totango_activities(account_ids)
        if not activities:
            logger.error("No activities found")
            return
            
        logger.info("Loading Gainsight data...")
        users = self.get_gainsight_users()
        
        # Get the company_id from config
        company_id = self.config['gainsight']['company_id']
        logger.info(f"Using company ID: {company_id}")
        
        activity_types = self.get_gainsight_activity_types(company_id)
        
        if not activity_types:
            logger.error("Failed to load activity types - aborting migration")
            return
        
        logger.info(f"📊 Migration Summary:")
        logger.info(f"  - Direct touchpoint mappings available: {len(self.touchpoint_id_mapping)}")
        logger.info(f"  - Direct flow type mappings available: {len(self.flow_type_id_mapping)}")
        logger.info(f"  - Processing {len(activities)} activities with attachment support...")
        
        for i, activity in enumerate(activities, 1):
            result = self.migrate_single_activity(activity, users, activity_types)
            
            if result['success']:
                self.results['success'] += 1
            else:
                self.results['failed'] += 1
                self.results['errors'].append(result)
            
            if i % 5 == 0:  # More frequent progress updates due to attachment processing
                logger.info(f"Progress: {i}/{len(activities)} ({i/len(activities)*100:.1f}%)")
            
            time.sleep(2.0)  # Longer delay due to attachment uploads
            
        end_time = time.time()
        self.generate_report(end_time - start_time)
        self.save_results()
    
    def generate_report(self, total_time: float):
        total = self.results['success'] + self.results['failed']
        success_rate = (self.results['success'] / total * 100) if total > 0 else 0
        
        report = f"""
🚀 ENHANCED TOTANGO TO GAINSIGHT MIGRATION REPORT (WITH ATTACHMENTS)
{'='*75}
📊 STATISTICS:
    Total Activities: {total:,}
    ✅ Successful: {self.results['success']:,}
    ❌ Failed: {self.results['failed']:,}
    📈 Success Rate: {success_rate:.1f}%
    ⏱️ Total Time: {total_time:.1f} seconds

📎 ATTACHMENT STATISTICS:
    ✅ Attachments Processed: {self.results['attachments_processed']}
    ✅ Attachments Uploaded: {self.results['attachments_uploaded']}
    ❌ Attachments Failed: {self.results['attachments_failed']}

🎯 MAPPING STATISTICS:
    ✅ Touchpoint Reason Mappings Applied: {self.results['mappings_applied']['touchpoint_reason_mappings']}
    ✅ Flow Type Mappings Applied: {self.results['mappings_applied']['flow_type_mappings']}

🎯 GAINSIGHT ACTIVITY TYPES:
"""
        
        for type_name, type_id in self.results['activity_types'].items():
            report += f"    {type_name}: {type_id}\n"
        
        if self.results['errors']:
            report += f"""
❌ ERRORS (first 3):
"""
            for error in self.results['errors'][:3]:
                report += f"    {error.get('account_id', 'unknown')}: {error.get('error', 'unknown')}\n"
                
        report += f"""
{'='*75}
🎉 Enhanced migration with attachments completed!
"""
        
        print(report)
    
    def save_results(self):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_dir = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram"
        
        results_file = os.path.join(base_dir, f"enhanced_results_{timestamp}.json")
        with open(results_file, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        
        logger.info(f"Results saved: {results_file}")

def main():
    print("🚀 ENHANCED Totango to Gainsight Migrator with Attachment Support")
    print("="*75)
    
    # Use the actual Totango account ID from the URL you provided
    account_ids = [
        '001b000003nwKk1AAE'  # ICICI Bank account from Totango
    ]
    
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    
    try:
        migrator = EnhancedTotangoGainsightMigrator(config_file)
        migrator.migrate_all_activities(account_ids)
        logger.info("✅ Enhanced migration with attachments completed!")
        
    except Exception as e:
        logger.error(f"Migration failed: {e}")

if __name__ == "__main__":
    main()
