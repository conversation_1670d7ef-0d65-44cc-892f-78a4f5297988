#!/usr/bin/env python3
"""
Run the Fixed Attachment Migrator
Simple script to run the fixed migrator with proper configuration
"""

import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from fixed_attachment_migrator import FixedAttachmentMigrator

def main():
    print("🚀 RUNNING FIXED ATTACHMENT MIGRATOR")
    print("="*50)
    print()
    
    # Configuration
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    account_ids = ['001b000003nwKk1AAE']  # Your ICICI Bank account
    
    print(f"📋 Configuration:")
    print(f"   Config file: {config_file}")
    print(f"   Account IDs: {account_ids}")
    print(f"   Expected: Activities with attachments will be migrated")
    print()
    
    try:
        # Create and run the fixed migrator
        migrator = FixedAttachmentMigrator(config_file)
        
        print("🔄 Starting migration with FIXED attachment handling...")
        print()
        
        migrator.migrate_all_activities(account_ids)
        
        print()
        print("✅ MIGRATION COMPLETED!")
        print("Check the logs above for detailed results.")
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
