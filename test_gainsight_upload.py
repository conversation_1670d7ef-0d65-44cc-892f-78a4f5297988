#!/usr/bin/env python3
"""
Gainsight Upload Format Tester
Tests the exact format needed for Gainsight attachment uploads
"""

import json
import requests
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("GainsightUploadTester")

class GainsightUploadTester:
    
    def __init__(self, config_file):
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        self.session = requests.Session()
    
    def get_mime_type(self, extension: str) -> str:
        """Get MIME type for file extension"""
        extension = extension.lower().lstrip('.')
        
        mime_types = {
            'pdf': 'application/pdf',
            'png': 'image/png',
            'jpg': 'image/jpeg',
            'jpeg': 'image/jpeg',
            'gif': 'image/gif',
            'txt': 'text/plain',
            'doc': 'application/msword',
            'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        }
        
        return mime_types.get(extension, 'application/octet-stream')
    
    def test_upload_formats(self):
        """Test different upload formats to see which one works"""
        logger.info("🧪 Testing Gainsight upload formats...")
        
        # Create a simple test file
        test_content = b"Test file content for Gainsight upload validation"
        test_filename = "test_upload_validation.txt"
        
        upload_url = f"{self.config['gainsight']['url']}/v1/ant/attachments"
        
        # Test Format 1: Current format
        logger.info("\n📋 TEST FORMAT 1: Current Implementation")
        self.test_format_1(upload_url, test_filename, test_content)
        
        # Test Format 2: Simplified format
        logger.info("\n📋 TEST FORMAT 2: Simplified Format")
        self.test_format_2(upload_url, test_filename, test_content)
        
        # Test Format 3: Minimal format
        logger.info("\n📋 TEST FORMAT 3: Minimal Format")
        self.test_format_3(upload_url, test_filename, test_content)
        
        # Test Format 4: Alternative context format
        logger.info("\n📋 TEST FORMAT 4: Alternative Context Format")
        self.test_format_4(upload_url, test_filename, test_content)
    
    def test_format_1(self, url: str, filename: str, content: bytes):
        """Test current implementation format"""
        try:
            files = {
                'file': (filename, content, 'text/plain')
            }
            
            form_data = {
                'contexts': json.dumps([{
                    'id': self.config['gainsight']['company_id'],
                    'base': True,
                    'obj': 'Company',
                    'lbl': self.config['gainsight']['company_name'],
                    'eid': None,
                    'eobj': 'Account',
                    'eurl': None,
                    'esys': 'SALESFORCE',
                    'dsp': True
                }]),
                'source': 'C360'
            }
            
            headers = {k: v for k, v in self.config['gainsight']['headers'].items() 
                      if k.lower() not in ['content-type', 'content-length']}
            
            response = self.session.post(url, files=files, data=form_data, headers=headers, timeout=60)
            self.log_response("Format 1", response)
            
        except Exception as e:
            logger.error(f"❌ Format 1 error: {e}")
    
    def test_format_2(self, url: str, filename: str, content: bytes):
        """Test simplified format"""
        try:
            files = {
                'file': (filename, content, 'text/plain')
            }
            
            form_data = {
                'contexts': json.dumps([{
                    'id': self.config['gainsight']['company_id'],
                    'obj': 'Company'
                }])
            }
            
            headers = {k: v for k, v in self.config['gainsight']['headers'].items() 
                      if k.lower() not in ['content-type', 'content-length']}
            
            response = self.session.post(url, files=files, data=form_data, headers=headers, timeout=60)
            self.log_response("Format 2", response)
            
        except Exception as e:
            logger.error(f"❌ Format 2 error: {e}")
    
    def test_format_3(self, url: str, filename: str, content: bytes):
        """Test minimal format"""
        try:
            files = {
                'file': (filename, content, 'text/plain')
            }
            
            # No additional form data
            headers = {k: v for k, v in self.config['gainsight']['headers'].items() 
                      if k.lower() not in ['content-type', 'content-length']}
            
            response = self.session.post(url, files=files, headers=headers, timeout=60)
            self.log_response("Format 3", response)
            
        except Exception as e:
            logger.error(f"❌ Format 3 error: {e}")
    
    def test_format_4(self, url: str, filename: str, content: bytes):
        """Test alternative context format"""
        try:
            files = {
                'file': (filename, content, 'text/plain')
            }
            
            form_data = {
                'contexts': json.dumps([{
                    'id': self.config['gainsight']['company_id'],
                    'base': True,
                    'obj': 'Company',
                    'lbl': self.config['gainsight'].get('company_name', 'Test Company'),
                    'esys': 'SALESFORCE',
                    'dsp': True
                }]),
                'source': 'C360'
            }
            
            headers = {k: v for k, v in self.config['gainsight']['headers'].items() 
                      if k.lower() not in ['content-type', 'content-length']}
            
            response = self.session.post(url, files=files, data=form_data, headers=headers, timeout=60)
            self.log_response("Format 4", response)
            
        except Exception as e:
            logger.error(f"❌ Format 4 error: {e}")
    
    def log_response(self, format_name: str, response):
        """Log response details"""
        logger.info(f"  {format_name} Response: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get('result') and result.get('data'):
                    attachment_id = result['data'].get('id')
                    logger.info(f"  ✅ SUCCESS! Attachment ID: {attachment_id}")
                    return True
                else:
                    logger.info(f"  ❌ Success but no attachment ID")
            except:
                logger.info(f"  ❌ Success but invalid JSON")
        else:
            logger.info(f"  ❌ Failed: {response.text[:200]}")
        
        return False
    
    def debug_headers_and_config(self):
        """Debug current configuration"""
        logger.info("🔍 DEBUGGING CONFIGURATION:")
        logger.info(f"  Gainsight URL: {self.config['gainsight']['url']}")
        logger.info(f"  Company ID: {self.config['gainsight']['company_id']}")
        logger.info(f"  Company Name: {self.config['gainsight'].get('company_name', 'NOT SET')}")
        logger.info(f"  User ID: {self.config['gainsight'].get('user_id', 'NOT SET')}")
        
        headers = self.config['gainsight']['headers']
        logger.info(f"  Headers present: {list(headers.keys())}")
        
        # Check for authentication
        if 'Cookie' in headers:
            logger.info(f"  Authentication: Cookie-based")
        elif 'Authorization' in headers:
            logger.info(f"  Authentication: Bearer token")
        else:
            logger.warning(f"  ⚠️ No obvious authentication method found")

def main():
    print("🧪 GAINSIGHT UPLOAD FORMAT TESTER")
    print("="*40)
    
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    
    try:
        tester = GainsightUploadTester(config_file)
        
        # Debug configuration first
        tester.debug_headers_and_config()
        
        # Test different upload formats
        tester.test_upload_formats()
        
        print("\n" + "="*60)
        print("🎯 UPLOAD FORMAT TEST COMPLETED")
        print("="*60)
        print("Check the logs above to see which format works.")
        print("The working format will be used to fix the migrator.")
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")

if __name__ == "__main__":
    main()
