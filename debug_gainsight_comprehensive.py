#!/usr/bin/env python3
"""
Comprehensive Gainsight Upload Debugger
Tests different upload approaches and captures detailed error information
"""

import json
import requests
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("GainsightUploadDebugger")

class GainsightUploadDebugger:
    
    def __init__(self, config_file):
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        self.session = requests.Session()
    
    def test_endpoint_accessibility(self):
        """Test if the upload endpoint is accessible"""
        logger.info("🔍 Testing Gainsight endpoint accessibility...")
        
        endpoints_to_test = [
            f"{self.config['gainsight']['url']}/v1/ant/attachments",
            f"{self.config['gainsight']['url']}/v1/ant/upload",
            f"{self.config['gainsight']['url']}/v1/ant/files",
            f"{self.config['gainsight']['url']}/v1/ant/storage/upload",
            f"{self.config['gainsight']['url']}/v1/ant/attachments/upload"
        ]
        
        for endpoint in endpoints_to_test:
            try:
                logger.info(f"📡 Testing: {endpoint}")
                
                # Test with GET first to see if endpoint exists
                response = self.session.get(endpoint, headers=self.config['gainsight']['headers'], timeout=30)
                logger.info(f"  GET Response: {response.status_code}")
                
                if response.status_code != 404:
                    # Test with OPTIONS to see allowed methods
                    options_response = self.session.options(endpoint, headers=self.config['gainsight']['headers'], timeout=30)
                    logger.info(f"  OPTIONS Response: {options_response.status_code}")
                    logger.info(f"  Allowed methods: {options_response.headers.get('Allow', 'None')}")
                
            except Exception as e:
                logger.info(f"  ❌ Error: {e}")
    
    def test_authentication(self):
        """Test if authentication is working"""
        logger.info("🔐 Testing Gainsight authentication...")
        
        # Test a known working endpoint first
        test_endpoints = [
            f"{self.config['gainsight']['url']}/v1/ant/forms",
            f"{self.config['gainsight']['url']}/v1/user/me",
            f"{self.config['gainsight']['url']}/v1/settings/user"
        ]
        
        for endpoint in test_endpoints:
            try:
                logger.info(f"📡 Testing auth with: {endpoint}")
                response = self.session.get(endpoint, headers=self.config['gainsight']['headers'], timeout=30)
                logger.info(f"  Response: {response.status_code}")
                
                if response.status_code == 200:
                    logger.info(f"  ✅ Authentication works!")
                    return True
                elif response.status_code == 401:
                    logger.error(f"  ❌ Authentication failed (401)")
                elif response.status_code == 403:
                    logger.error(f"  ❌ Access forbidden (403)")
                    
            except Exception as e:
                logger.info(f"  ❌ Error: {e}")
        
        return False
    
    def capture_detailed_upload_error(self):
        """Capture detailed error information from upload attempt"""
        logger.info("🧪 Capturing detailed upload error information...")
        
        test_content = b"Test file for detailed error analysis"
        test_filename = "debug_test.txt"
        
        upload_url = f"{self.config['gainsight']['url']}/v1/ant/attachments"
        
        try:
            files = {
                'file': (test_filename, test_content, 'text/plain')
            }
            
            form_data = {
                'contexts': json.dumps([{
                    'id': self.config['gainsight']['company_id'],
                    'base': True,
                    'obj': 'Company',
                    'lbl': self.config['gainsight']['company_name'],
                    'eid': None,
                    'eobj': 'Account',
                    'eurl': None,
                    'esys': 'SALESFORCE',
                    'dsp': True
                }]),
                'source': 'C360'
            }
            
            headers = {k: v for k, v in self.config['gainsight']['headers'].items() 
                      if k.lower() not in ['content-type', 'content-length']}
            
            logger.info(f"📤 Making upload request to: {upload_url}")
            logger.info(f"📋 Request headers: {list(headers.keys())}")
            logger.info(f"📋 Form data keys: {list(form_data.keys())}")
            logger.info(f"📋 File info: {test_filename}, {len(test_content)} bytes")
            
            response = self.session.post(
                upload_url,
                files=files,
                data=form_data,
                headers=headers,
                timeout=60
            )
            
            logger.info(f"📥 Response status: {response.status_code}")
            logger.info(f"📥 Response headers: {dict(response.headers)}")
            logger.info(f"📥 Response content: {response.text}")
            
            # Try to parse as JSON
            try:
                json_response = response.json()
                logger.info(f"📋 Parsed JSON: {json.dumps(json_response, indent=2)}")
            except:
                logger.info(f"📋 Response is not valid JSON")
            
        except Exception as e:
            logger.error(f"❌ Request error: {e}")
    
    def test_alternative_upload_approaches(self):
        """Test alternative upload approaches"""
        logger.info("🔄 Testing alternative upload approaches...")
        
        # Test 1: JSON payload instead of multipart
        logger.info("\n📋 TEST: JSON payload upload")
        self.test_json_upload()
        
        # Test 2: Raw file upload
        logger.info("\n📋 TEST: Raw file upload") 
        self.test_raw_upload()
        
        # Test 3: Base64 encoded upload
        logger.info("\n📋 TEST: Base64 encoded upload")
        self.test_base64_upload()
    
    def test_json_upload(self):
        """Test JSON-based upload"""
        try:
            import base64
            
            test_content = b"Test file content"
            encoded_content = base64.b64encode(test_content).decode('utf-8')
            
            payload = {
                'name': 'test_json_upload.txt',
                'content': encoded_content,
                'contentType': 'text/plain',
                'contexts': [{
                    'id': self.config['gainsight']['company_id'],
                    'obj': 'Company'
                }],
                'source': 'C360'
            }
            
            upload_url = f"{self.config['gainsight']['url']}/v1/ant/attachments"
            
            response = self.session.post(
                upload_url,
                json=payload,
                headers=self.config['gainsight']['headers'],
                timeout=60
            )
            
            logger.info(f"  JSON upload response: {response.status_code}")
            if response.text:
                logger.info(f"  Response: {response.text[:200]}")
                
        except Exception as e:
            logger.info(f"  ❌ JSON upload error: {e}")
    
    def test_raw_upload(self):
        """Test raw file upload"""
        try:
            test_content = b"Test file content"
            
            upload_url = f"{self.config['gainsight']['url']}/v1/ant/attachments"
            
            headers = self.config['gainsight']['headers'].copy()
            headers['Content-Type'] = 'text/plain'
            headers['Content-Disposition'] = 'attachment; filename="test_raw.txt"'
            
            response = self.session.post(
                upload_url,
                data=test_content,
                headers=headers,
                timeout=60
            )
            
            logger.info(f"  Raw upload response: {response.status_code}")
            if response.text:
                logger.info(f"  Response: {response.text[:200]}")
                
        except Exception as e:
            logger.info(f"  ❌ Raw upload error: {e}")
    
    def test_base64_upload(self):
        """Test base64 encoded upload"""
        try:
            import base64
            
            test_content = b"Test file content"
            encoded_content = base64.b64encode(test_content).decode('utf-8')
            
            form_data = {
                'file_content': encoded_content,
                'file_name': 'test_base64.txt',
                'content_type': 'text/plain',
                'contexts': json.dumps([{
                    'id': self.config['gainsight']['company_id'],
                    'obj': 'Company'
                }])
            }
            
            upload_url = f"{self.config['gainsight']['url']}/v1/ant/attachments"
            
            response = self.session.post(
                upload_url,
                data=form_data,
                headers=self.config['gainsight']['headers'],
                timeout=60
            )
            
            logger.info(f"  Base64 upload response: {response.status_code}")
            if response.text:
                logger.info(f"  Response: {response.text[:200]}")
                
        except Exception as e:
            logger.info(f"  ❌ Base64 upload error: {e}")
    
    def test_presigned_url_approach(self):
        """Test if Gainsight uses pre-signed URLs for upload"""
        logger.info("🔗 Testing pre-signed URL approach...")
        
        # Check for endpoints that might provide upload URLs
        presign_endpoints = [
            f"{self.config['gainsight']['url']}/v1/ant/attachments/presign",
            f"{self.config['gainsight']['url']}/v1/ant/upload/url",
            f"{self.config['gainsight']['url']}/v1/ant/storage/presign",
            f"{self.config['gainsight']['url']}/v1/ant/attachments/upload-url"
        ]
        
        for endpoint in presign_endpoints:
            try:
                logger.info(f"📡 Testing: {endpoint}")
                
                # Try POST request for getting presigned URL
                payload = {
                    'fileName': 'test.txt',
                    'contentType': 'text/plain',
                    'contexts': [{
                        'id': self.config['gainsight']['company_id'],
                        'obj': 'Company'
                    }]
                }
                
                response = self.session.post(
                    endpoint,
                    json=payload,
                    headers=self.config['gainsight']['headers'],
                    timeout=30
                )
                
                logger.info(f"  Response: {response.status_code}")
                if response.status_code == 200:
                    logger.info(f"  ✅ Found presigned URL endpoint!")
                    logger.info(f"  Response: {response.text}")
                    return endpoint
                    
            except Exception as e:
                logger.info(f"  ❌ Error: {e}")
        
        return None
    
    def run_comprehensive_debug(self):
        """Run comprehensive debugging"""
        logger.info("🚀 Starting comprehensive Gainsight upload debugging")
        logger.info("="*60)
        
        # Step 1: Test basic connectivity and auth
        logger.info("\n🔐 STEP 1: Authentication & Connectivity")
        auth_works = self.test_authentication()
        
        # Step 2: Test endpoint accessibility
        logger.info("\n📡 STEP 2: Endpoint Accessibility")
        self.test_endpoint_accessibility()
        
        # Step 3: Capture detailed error
        logger.info("\n🧪 STEP 3: Detailed Error Analysis")
        self.capture_detailed_upload_error()
        
        # Step 4: Test alternative approaches
        logger.info("\n🔄 STEP 4: Alternative Upload Approaches")
        self.test_alternative_upload_approaches()
        
        # Step 5: Test presigned URL approach
        logger.info("\n🔗 STEP 5: Pre-signed URL Testing")
        presign_endpoint = self.test_presigned_url_approach()
        
        # Summary
        logger.info("\n" + "="*60)
        logger.info("🎯 DEBUGGING SUMMARY")
        logger.info("="*60)
        logger.info(f"Authentication working: {'✅ YES' if auth_works else '❌ NO'}")
        logger.info(f"Pre-signed URL endpoint: {'✅ FOUND' if presign_endpoint else '❌ NOT FOUND'}")
        logger.info("Check the detailed logs above for the exact issue.")

def main():
    print("🔍 COMPREHENSIVE GAINSIGHT UPLOAD DEBUGGER")
    print("="*50)
    
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    
    try:
        debugger = GainsightUploadDebugger(config_file)
        debugger.run_comprehensive_debug()
        
    except Exception as e:
        logger.error(f"❌ Debug execution failed: {e}")

if __name__ == "__main__":
    main()
