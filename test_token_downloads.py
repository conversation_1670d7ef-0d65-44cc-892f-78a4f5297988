#!/usr/bin/env python3
"""
Fixed Totango Download with JWT Token Support
Implements proper token-based downloads using the ?token= parameter approach
"""

import json
import requests
import logging
from datetime import datetime
from typing import Dict, Optional
import base64

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("TotangoTokenDownloader")

class TotangoTokenDownloader:
    
    def __init__(self, config_file):
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        self.session = requests.Session()
    
    def get_download_token(self) -> Optional[str]:
        """Get a JWT download token from Totango"""
        try:
            # Try different token endpoints
            token_endpoints = [
                f"{self.config['totango']['url']}/t01/mend/api/v2/auth/token",
                f"{self.config['totango']['url']}/t01/mend/api/v2/download/token", 
                f"{self.config['totango']['url']}/t01/mend/api/v2/assets/token",
                f"{self.config['totango']['url']}/api/v2/auth/download-token"
            ]
            
            for endpoint in token_endpoints:
                try:
                    logger.info(f"🔑 Trying token endpoint: {endpoint}")
                    
                    response = self.session.get(
                        endpoint,
                        headers=self.config['totango']['headers'],
                        timeout=30
                    )
                    
                    logger.info(f"  Response: {response.status_code}")
                    
                    if response.status_code == 200:
                        result = response.json()
                        logger.info(f"  Response keys: {list(result.keys()) if isinstance(result, dict) else 'not dict'}")
                        
                        # Look for token in various possible fields
                        token = (result.get('token') or 
                                result.get('downloadToken') or 
                                result.get('access_token') or
                                result.get('jwt') or
                                result.get('authToken'))
                        
                        if token:
                            logger.info(f"✅ Got token from {endpoint}")
                            return token
                    
                except Exception as e:
                    logger.info(f"  Error: {e}")
                    continue
            
            logger.warning("⚠️ Could not get token from any endpoint")
            return None
            
        except Exception as e:
            logger.error(f"❌ Error getting download token: {e}")
            return None
    
    def extract_token_from_headers(self) -> Optional[str]:
        """Extract token information from session headers/cookies"""
        try:
            headers = self.config['totango']['headers']
            
            # Check if there's already a token in Authorization header
            auth_header = headers.get('Authorization', '')
            if 'Bearer ' in auth_header:
                token = auth_header.replace('Bearer ', '')
                logger.info(f"🔑 Found token in Authorization header")
                return token
            
            # Check cookies for token-like values
            cookie_header = headers.get('Cookie', '')
            if cookie_header:
                # Look for JWT-like patterns in cookies
                import re
                jwt_pattern = r'[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+\.[A-Za-z0-9_-]+'
                matches = re.findall(jwt_pattern, cookie_header)
                
                for match in matches:
                    if len(match) > 100:  # JWT tokens are typically long
                        logger.info(f"🔑 Found potential JWT in cookies")
                        return match
            
            return None
            
        except Exception as e:
            logger.error(f"❌ Error extracting token from headers: {e}")
            return None
    
    def generate_download_url_with_token(self, download_path: str, token: str) -> str:
        """Generate the full download URL with token parameter"""
        # Parse the download path to construct assets-proxy URL
        path_parts = download_path.strip('/').split('/')
        service_id = path_parts[0]  # e.g., "45606"
        file_path = '/'.join(path_parts[1:])  # remaining path
        
        # Construct the assets-proxy URL with token
        base_url = "https://assets-proxy.totango.com/api/v2/assets"
        download_url = f"{base_url}/{service_id}/{file_path}?token={token}"
        
        return download_url
    
    def test_token_download(self, asset_id: str, download_path: str, asset_name: str):
        """Test downloading with JWT token approach"""
        logger.info(f"🧪 Testing token download for: {asset_name}")
        logger.info(f"  Asset ID: {asset_id}")
        logger.info(f"  Download Path: {download_path}")
        
        # Method 1: Try to get a fresh token
        token = self.get_download_token()
        
        if not token:
            # Method 2: Extract token from existing headers/cookies
            token = self.extract_token_from_headers()
        
        if not token:
            logger.error(f"❌ Could not obtain download token")
            return False
        
        # Construct download URL with token
        download_url = self.generate_download_url_with_token(download_path, token)
        logger.info(f"🔗 Download URL: {download_url}")
        
        try:
            # Try downloading with the token URL
            response = self.session.get(
                download_url,
                timeout=60,
                stream=True  # Use streaming for large files
            )
            
            logger.info(f"📥 Download response: {response.status_code}")
            
            if response.status_code == 200:
                # Calculate content length
                content_length = len(response.content)
                logger.info(f"✅ SUCCESS: Downloaded {content_length} bytes")
                return True
            elif response.status_code == 401:
                logger.error(f"❌ Token authentication failed (401)")
                return False
            elif response.status_code == 403:
                logger.error(f"❌ Token expired or insufficient permissions (403)")
                return False
            elif response.status_code == 404:
                logger.error(f"❌ File not found (404)")
                return False
            else:
                logger.error(f"❌ Download failed: {response.status_code}")
                logger.error(f"Response: {response.text[:200]}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Download error: {e}")
            return False
    
    def test_manual_token(self, asset_id: str, download_path: str, asset_name: str, manual_token: str):
        """Test download with a manually provided token"""
        logger.info(f"🧪 Testing with manual token for: {asset_name}")
        
        download_url = self.generate_download_url_with_token(download_path, manual_token)
        logger.info(f"🔗 Manual token URL: {download_url}")
        
        try:
            response = self.session.get(download_url, timeout=60)
            logger.info(f"📥 Manual token response: {response.status_code}")
            
            if response.status_code == 200:
                logger.info(f"✅ MANUAL TOKEN SUCCESS: {len(response.content)} bytes")
                return True
            else:
                logger.error(f"❌ Manual token failed: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Manual token error: {e}")
            return False
    
    def run_comprehensive_test(self, account_id: str):
        """Run comprehensive download tests"""
        logger.info(f"🚀 Running comprehensive token download test for account: {account_id}")
        
        # Get test assets from Totango
        url = f"{self.config['totango']['url']}/t01/mend/api/v2/events/"
        params = {
            'account_id': account_id,
            'include_formatting': 'true'
        }
        
        response = self.session.get(
            url,
            headers=self.config['totango']['headers'],
            params=params,
            timeout=30
        )
        
        if response.status_code != 200:
            logger.error(f"❌ Failed to get activities: {response.status_code}")
            return
        
        events = response.json()
        
        # Find test assets
        test_assets = []
        for event in events:
            properties = event.get('properties', {})
            if 'meeting_type' in properties and properties.get('assets'):
                for asset_str in properties['assets']:
                    try:
                        if isinstance(asset_str, str):
                            asset = json.loads(asset_str)
                        else:
                            asset = asset_str
                        
                        if asset.get('asset_type') == 'file' and asset.get('downloadPath'):
                            test_assets.append(asset)
                            if len(test_assets) >= 3:
                                break
                    except:
                        continue
                if len(test_assets) >= 3:
                    break
        
        if not test_assets:
            logger.warning("⚠️ No file assets found to test")
            return
        
        logger.info(f"📁 Found {len(test_assets)} assets to test")
        
        # Test each asset
        success_count = 0
        for i, asset in enumerate(test_assets, 1):
            logger.info(f"\n{'='*60}")
            logger.info(f"🧪 TESTING ASSET {i}: {asset.get('name', 'Unknown')}")
            logger.info(f"{'='*60}")
            
            if self.test_token_download(
                asset.get('id', ''),
                asset.get('downloadPath', ''),
                asset.get('name', 'Unknown')
            ):
                success_count += 1
        
        # Test with the example token you provided
        logger.info(f"\n{'='*60}")
        logger.info(f"🧪 TESTING WITH YOUR EXAMPLE TOKEN")
        logger.info(f"{'='*60}")
        
        example_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VybmFtZSI6ImYwMTEzMTc4LTM2NWQtMTFmMC04YTBjLTBhZmZmNzU2Y2M0ZiIsInNlcnZpY2VJZCI6IjQ1NjA2IiwiaWF0IjoxNzQ5MDMxMzYxLCJleHAiOjE3NDkxMTc3NjF9.LIQqBywmmO00TbMWYjHVSReiKf2n4NsDeXBRClAwRc0"
        
        if test_assets:
            if self.test_manual_token(
                test_assets[0].get('id', ''),
                test_assets[0].get('downloadPath', ''),
                test_assets[0].get('name', 'Unknown'),
                example_token
            ):
                success_count += 1
        
        # Summary
        logger.info(f"\n{'='*60}")
        logger.info(f"🎯 TEST SUMMARY")
        logger.info(f"{'='*60}")
        logger.info(f"Assets tested: {len(test_assets) + 1}")
        logger.info(f"Successful downloads: {success_count}")
        logger.info(f"Success rate: {success_count/(len(test_assets)+1)*100:.1f}%")
        
        if success_count > 0:
            logger.info(f"✅ TOKEN APPROACH WORKS! Ready to implement in migrator.")
        else:
            logger.info(f"❌ Need to investigate token generation method.")

def main():
    print("🔑 TOTANGO JWT TOKEN DOWNLOAD TESTER")
    print("="*50)
    
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    account_id = "001b000003nwKk1AAE"
    
    try:
        tester = TotangoTokenDownloader(config_file)
        tester.run_comprehensive_test(account_id)
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")

if __name__ == "__main__":
    main()
