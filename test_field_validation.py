#!/usr/bin/env python3
"""
Detailed Field Validation Test for Gainsight Upload
Tests each field individually to find the validation issue
"""

import json
import requests
import logging

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("FieldValidationTest")

def test_field_combinations():
    """Test different field combinations to isolate the validation issue"""
    
    # Load config
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    # Test file
    test_content = b"Field validation test"
    test_filename = "field_test.txt"
    
    files = {
        'file': (test_filename, test_content, 'text/plain')
    }
    
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Cookie': config['gainsight']['headers']['<PERSON>ie'],
        'Origin': config['gainsight']['url'],
        'Referer': f"{config['gainsight']['url']}/v1/ui/customersuccess360",
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'X-Gs-Host': 'GAINSIGHT'
    }
    
    upload_url = f"{config['gainsight']['url']}/v1/ant/attachments"
    
    # Test different field combinations
    test_cases = [
        # Test 1: Minimal - just file
        {
            'name': 'File only',
            'data': {}
        },
        
        # Test 2: Just contexts
        {
            'name': 'File + contexts',
            'data': {
                'contexts': json.dumps([{
                    'id': config['gainsight']['company_id'],
                    'obj': 'Company'
                }])
            }
        },
        
        # Test 3: File + contexts + source
        {
            'name': 'File + contexts + source',
            'data': {
                'contexts': json.dumps([{
                    'id': config['gainsight']['company_id'],
                    'obj': 'Company'
                }]),
                'source': 'C360'
            }
        },
        
        # Test 4: File + contexts + source + type
        {
            'name': 'File + contexts + source + type',
            'data': {
                'contexts': json.dumps([{
                    'id': config['gainsight']['company_id'],
                    'obj': 'Company'
                }]),
                'source': 'C360',
                'type': 'DEFAULT'
            }
        },
        
        # Test 5: File + contexts + source + simplified user
        {
            'name': 'File + contexts + source + simple user',
            'data': {
                'contexts': json.dumps([{
                    'id': config['gainsight']['company_id'],
                    'obj': 'Company'
                }]),
                'source': 'C360',
                'user': json.dumps({
                    'id': config['gainsight']['user_id']
                })
            }
        },
        
        # Test 6: Without entityId (maybe it's the problem)
        {
            'name': 'All fields except entityId',
            'data': {
                'contexts': json.dumps([{
                    'id': config['gainsight']['company_id'],
                    'base': True,
                    'obj': 'Company',
                    'lbl': config['gainsight']['company_name'],
                    'eid': None,
                    'eobj': 'Account',
                    'eurl': None,
                    'esys': 'SALESFORCE',
                    'dsp': True
                }]),
                'source': 'C360',
                'user': json.dumps({
                    'id': config['gainsight']['user_id'],
                    'obj': 'User',
                    'name': config['gainsight']['user_name'],
                    'email': config['gainsight']['user_email'],
                    'esys': 'SALESFORCE',
                    'sys': 'GAINSIGHT'
                }),
                'type': 'DEFAULT'
            }
        },
        
        # Test 7: Empty entityId
        {
            'name': 'Empty entityId',
            'data': {
                'entityId': '',
                'contexts': json.dumps([{
                    'id': config['gainsight']['company_id'],
                    'obj': 'Company'
                }]),
                'source': 'C360',
                'type': 'DEFAULT'
            }
        },
        
        # Test 8: Different entityId format (maybe generate one)
        {
            'name': 'Generated entityId',
            'data': {
                'entityId': 'TEST123456789',
                'contexts': json.dumps([{
                    'id': config['gainsight']['company_id'],
                    'obj': 'Company'
                }]),
                'source': 'C360',
                'type': 'DEFAULT'
            }
        }
    ]
    
    logger.info("🧪 Testing different field combinations...")
    
    for i, test_case in enumerate(test_cases, 1):
        logger.info(f"\n📋 TEST {i}: {test_case['name']}")
        logger.info(f"  Fields: {list(test_case['data'].keys())}")
        
        try:
            response = requests.post(
                upload_url,
                files=files,
                data=test_case['data'],
                headers=headers,
                timeout=60
            )
            
            logger.info(f"  Response: {response.status_code}")
            
            if response.status_code == 200:
                logger.info(f"  🎉 SUCCESS! This combination works!")
                try:
                    result = response.json()
                    if result.get('data'):
                        logger.info(f"    Attachment ID: {result['data'].get('id')}")
                except:
                    pass
                return test_case
                
            elif response.status_code == 400:
                logger.info(f"  ❌ Bad Request (400)")
                if response.text:
                    logger.info(f"    Response: {response.text[:200]}")
                    
            elif response.status_code == 405:
                logger.info(f"  ❌ Method Not Allowed (405)")
                
            else:
                logger.info(f"  ❌ Status: {response.status_code}")
                if response.text:
                    logger.info(f"    Response: {response.text[:100]}")
                    
        except Exception as e:
            logger.error(f"  ❌ Error: {e}")
    
    return None

def test_create_activity_first():
    """Test creating an activity first to get a valid entityId"""
    
    logger.info("\n🔄 Testing: Create activity first to get valid entityId")
    
    # Load config
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    with open(config_file, 'r') as f:
        config = json.load(f)
    
    # Try to create a simple test activity first
    activity_payload = {
        'note': {
            'type': 'UPDATE',
            'subject': 'Test Activity for Attachment',
            'content': '<p>Test activity to get entityId for attachment upload</p>',
            'plainText': 'Test activity to get entityId for attachment upload',
            'activityDate': '2025-06-04T13:00:00.000Z'
        },
        'contexts': [{
            'id': config['gainsight']['company_id'],
            'base': True,
            'obj': 'Company',
            'lbl': config['gainsight']['company_name'],
            'esys': 'SALESFORCE',
            'dsp': True
        }],
        'author': {
            'id': config['gainsight']['user_id'],
            'obj': 'User',
            'name': config['gainsight']['user_name'],
            'email': config['gainsight']['user_email'],
            'esys': 'SALESFORCE',
            'sys': 'GAINSIGHT'
        },
        'meta': {
            'source': 'C360',
            'systemType': 'GAINSIGHT'
        }
    }
    
    headers = {
        'Content-Type': 'application/json',
        'Cookie': config['gainsight']['headers']['Cookie'],
        'Origin': config['gainsight']['url'],
        'Referer': f"{config['gainsight']['url']}/v1/ui/customersuccess360"
    }
    
    try:
        # Create draft first
        draft_url = f"{config['gainsight']['url']}/v1/ant/v2/activity/drafts"
        draft_response = requests.post(draft_url, json=activity_payload, headers=headers, timeout=60)
        
        logger.info(f"📝 Draft creation: {draft_response.status_code}")
        
        if draft_response.status_code == 200:
            draft_data = draft_response.json()
            draft_id = draft_data.get('data', {}).get('id')
            
            if draft_id:
                logger.info(f"✅ Draft created: {draft_id}")
                
                # Now try to upload attachment with this draft_id as entityId
                return test_attachment_with_entity_id(draft_id, config)
            else:
                logger.error("❌ No draft ID returned")
        else:
            logger.error(f"❌ Draft creation failed: {draft_response.text[:200]}")
            
    except Exception as e:
        logger.error(f"❌ Error creating activity: {e}")
    
    return False

def test_attachment_with_entity_id(entity_id, config):
    """Test attachment upload with a valid entity ID"""
    
    logger.info(f"📎 Testing attachment upload with entityId: {entity_id}")
    
    test_content = b"Test attachment with valid entityId"
    test_filename = "valid_entity_test.txt"
    
    files = {
        'file': (test_filename, test_content, 'text/plain')
    }
    
    form_data = {
        'entityId': entity_id,
        'contexts': json.dumps([{
            'id': config['gainsight']['company_id'],
            'base': True,
            'obj': 'Company',
            'lbl': config['gainsight']['company_name'],
            'eid': None,
            'eobj': 'Account',
            'eurl': None,
            'esys': 'SALESFORCE',
            'dsp': True
        }]),
        'source': 'C360',
        'user': json.dumps({
            'id': config['gainsight']['user_id'],
            'obj': 'User',
            'name': config['gainsight']['user_name'],
            'email': config['gainsight']['user_email'],
            'eid': None,
            'eobj': 'User',
            'epp': None,
            'esys': 'SALESFORCE',
            'sys': 'GAINSIGHT',
            'pp': ''
        }),
        'type': 'DEFAULT'
    }
    
    headers = {
        'Accept': 'application/json, text/plain, */*',
        'Cookie': config['gainsight']['headers']['Cookie'],
        'Origin': config['gainsight']['url'],
        'Referer': f"{config['gainsight']['url']}/v1/ui/customersuccess360",
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
        'X-Gs-Host': 'GAINSIGHT'
    }
    
    upload_url = f"{config['gainsight']['url']}/v1/ant/attachments"
    
    try:
        response = requests.post(
            upload_url,
            files=files,
            data=form_data,
            headers=headers,
            timeout=60
        )
        
        logger.info(f"📎 Attachment upload: {response.status_code}")
        
        if response.status_code == 200:
            logger.info("🎉 SUCCESS! Attachment uploaded with valid entityId!")
            try:
                result = response.json()
                if result.get('data'):
                    attachment_data = result['data']
                    logger.info(f"  Attachment ID: {attachment_data.get('id')}")
                    logger.info(f"  File URL: {attachment_data.get('url', '')[:100]}...")
            except:
                pass
            return True
        else:
            logger.error(f"❌ Upload failed: {response.status_code}")
            if response.text:
                logger.error(f"Response: {response.text[:200]}")
                
    except Exception as e:
        logger.error(f"❌ Error: {e}")
    
    return False

def main():
    print("🔍 DETAILED FIELD VALIDATION TEST")
    print("="*45)
    print("Testing each field combination to find validation issue")
    print()
    
    # Test different field combinations
    working_combination = test_field_combinations()
    
    if working_combination:
        print(f"\n🎉 FOUND WORKING COMBINATION!")
        print(f"Working fields: {list(working_combination['data'].keys())}")
    else:
        print("\n❌ No field combination worked")
        print("Testing with valid entityId from created activity...")
        
        # Test creating activity first
        success = test_create_activity_first()
        
        if success:
            print("\n🎉 SUCCESS! entityId was the issue!")
            print("Need to create Gainsight activity first, then upload attachments.")
        else:
            print("\n❌ Still not working. Need further investigation.")

if __name__ == "__main__":
    main()
