#!/usr/bin/env python3
"""
Totango Download Debug Script
Tests different download methods for Totango attachments
"""

import json
import requests
import logging
from datetime import datetime
from typing import Dict

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("DownloadTester")

class TotangoDownloadTester:
    
    def __init__(self, config_file):
        with open(config_file, 'r') as f:
            self.config = json.load(f)
        self.session = requests.Session()
    
    def test_download_methods(self, account_id: str):
        """Test different download methods for Totango attachments"""
        logger.info(f"🧪 Testing download methods for account: {account_id}")
        
        # First, get activities with assets
        url = f"{self.config['totango']['url']}/t01/mend/api/v2/events/"
        params = {
            'account_id': account_id,
            'include_formatting': 'true'
        }
        
        response = self.session.get(
            url,
            headers=self.config['totango']['headers'],
            params=params,
            timeout=30
        )
        
        if response.status_code != 200:
            logger.error(f"❌ Failed to get activities: {response.status_code}")
            return
        
        events = response.json()
        
        # Find activities with file assets
        test_assets = []
        for event in events:
            properties = event.get('properties', {})
            if 'meeting_type' in properties and properties.get('assets'):
                for asset_str in properties['assets']:
                    try:
                        if isinstance(asset_str, str):
                            asset = json.loads(asset_str)
                        else:
                            asset = asset_str
                        
                        if asset.get('asset_type') == 'file' and asset.get('downloadPath'):
                            test_assets.append(asset)
                            if len(test_assets) >= 3:  # Test max 3 files
                                break
                    except:
                        continue
                if len(test_assets) >= 3:
                    break
        
        if not test_assets:
            logger.warning("⚠️ No file assets found to test")
            return
        
        logger.info(f"📁 Found {len(test_assets)} file assets to test")
        
        # Test each asset with different methods
        for i, asset in enumerate(test_assets, 1):
            logger.info(f"\n{'='*60}")
            logger.info(f"🧪 TESTING ASSET {i}: {asset.get('name', 'Unknown')}")
            logger.info(f"{'='*60}")
            
            self.test_single_asset_download(asset)
    
    def test_single_asset_download(self, asset: Dict):
        """Test downloading a single asset with different methods"""
        name = asset.get('name', 'Unknown')
        asset_id = asset.get('id', '')
        download_path = asset.get('downloadPath', '')
        
        logger.info(f"📄 Asset: {name}")
        logger.info(f"🆔 ID: {asset_id}")
        logger.info(f"📂 Download Path: {download_path}")
        
        # Test Method 1: Token-based download
        logger.info(f"\n🧪 METHOD 1: Token-based download")
        self.test_token_download(asset_id, download_path)
        
        # Test Method 2: Direct path
        logger.info(f"\n🧪 METHOD 2: Direct path download")
        self.test_direct_download(download_path)
        
        # Test Method 3: Assets proxy
        logger.info(f"\n🧪 METHOD 3: Assets proxy download")
        self.test_assets_proxy_download(download_path)
        
        # Test Method 4: Asset metadata API
        logger.info(f"\n🧪 METHOD 4: Asset metadata API")
        self.test_asset_metadata_api(asset_id)
    
    def test_token_download(self, asset_id: str, download_path: str):
        """Test token-based download"""
        try:
            # Request download token
            token_url = f"{self.config['totango']['url']}/t01/mend/api/v2/assets/{asset_id}/download"
            
            response = self.session.get(
                token_url,
                headers=self.config['totango']['headers'],
                timeout=30
            )
            
            logger.info(f"  Token request: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"  Token response keys: {list(result.keys())}")
                
                download_url = result.get('url') or result.get('downloadUrl')
                if download_url:
                    logger.info(f"  Download URL: {download_url[:100]}...")
                    
                    # Try to download
                    file_response = self.session.get(
                        download_url,
                        headers=self.config['totango']['headers'],
                        timeout=30
                    )
                    
                    logger.info(f"  File download: {file_response.status_code}")
                    if file_response.status_code == 200:
                        logger.info(f"  ✅ SUCCESS: {len(file_response.content)} bytes")
                    else:
                        logger.info(f"  ❌ FAILED: {file_response.status_code}")
                else:
                    logger.info(f"  ❌ No download URL in response")
            else:
                logger.info(f"  ❌ FAILED: {response.status_code}")
                
        except Exception as e:
            logger.info(f"  ❌ ERROR: {e}")
    
    def test_direct_download(self, download_path: str):
        """Test direct path download"""
        try:
            direct_url = f"{self.config['totango']['url']}{download_path}"
            logger.info(f"  Direct URL: {direct_url}")
            
            response = self.session.get(
                direct_url,
                headers=self.config['totango']['headers'],
                timeout=30
            )
            
            logger.info(f"  Response: {response.status_code}")
            if response.status_code == 200:
                logger.info(f"  ✅ SUCCESS: {len(response.content)} bytes")
            else:
                logger.info(f"  ❌ FAILED: {response.status_code}")
                
        except Exception as e:
            logger.info(f"  ❌ ERROR: {e}")
    
    def test_assets_proxy_download(self, download_path: str):
        """Test assets proxy download"""
        try:
            path_parts = download_path.strip('/').split('/')
            if len(path_parts) >= 2:
                service_id = path_parts[0]
                file_path = '/'.join(path_parts[1:])
                
                proxy_url = f"https://assets-proxy.totango.com/api/v2/assets/{service_id}/{file_path}"
                logger.info(f"  Proxy URL: {proxy_url}")
                
                response = self.session.get(
                    proxy_url,
                    headers=self.config['totango']['headers'],
                    timeout=30
                )
                
                logger.info(f"  Response: {response.status_code}")
                if response.status_code == 200:
                    logger.info(f"  ✅ SUCCESS: {len(response.content)} bytes")
                else:
                    logger.info(f"  ❌ FAILED: {response.status_code}")
            else:
                logger.info(f"  ❌ Invalid path format")
                
        except Exception as e:
            logger.info(f"  ❌ ERROR: {e}")
    
    def test_asset_metadata_api(self, asset_id: str):
        """Test asset metadata API"""
        try:
            metadata_url = f"{self.config['totango']['url']}/t01/mend/api/v2/assets/{asset_id}"
            logger.info(f"  Metadata URL: {metadata_url}")
            
            response = self.session.get(
                metadata_url,
                headers=self.config['totango']['headers'],
                timeout=30
            )
            
            logger.info(f"  Response: {response.status_code}")
            if response.status_code == 200:
                result = response.json()
                logger.info(f"  Metadata keys: {list(result.keys())}")
                
                download_url = result.get('downloadUrl') or result.get('url')
                if download_url:
                    logger.info(f"  Found download URL: {download_url[:100]}...")
                    
                    file_response = self.session.get(
                        download_url,
                        headers=self.config['totango']['headers'],
                        timeout=30
                    )
                    
                    logger.info(f"  File download: {file_response.status_code}")
                    if file_response.status_code == 200:
                        logger.info(f"  ✅ SUCCESS: {len(file_response.content)} bytes")
                    else:
                        logger.info(f"  ❌ FAILED: {file_response.status_code}")
                else:
                    logger.info(f"  ❌ No download URL in metadata")
            else:
                logger.info(f"  ❌ FAILED: {response.status_code}")
                
        except Exception as e:
            logger.info(f"  ❌ ERROR: {e}")

def main():
    print("🧪 TOTANGO DOWNLOAD TESTER")
    print("="*40)
    
    config_file = "/Users/<USER>/Desktop/wild_weasel_gainsight_migration/totango_ram/migration_config.json"
    account_id = "001b000003nwKk1AAE"  # ICICI Bank account
    
    try:
        tester = TotangoDownloadTester(config_file)
        tester.test_download_methods(account_id)
        
        print("\n" + "="*60)
        print("🎯 DOWNLOAD TEST COMPLETED")
        print("="*60)
        print("Review the logs above to see which download method works.")
        print("The enhanced migrator will try all methods automatically.")
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")

if __name__ == "__main__":
    main()
